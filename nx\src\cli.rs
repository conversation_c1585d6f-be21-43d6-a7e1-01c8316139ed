// CLI module for handling command-line interface functionality

use clap::{Arg, Command};

fn print_header() {
    println!("NX - Node.js Package Manager");
    println!("Fast, reliable npm alternative built with Rust\n");
}

fn suggest_command(input: &str) -> Option<&'static str> {
    match input {
        "instal" | "intall" | "isntall" => Some("install"),
        "uninstal" | "unintsall" | "remove" => Some("uninstall"),
        "runn" | "rnu" => Some("run"),
        "lsit" | "lit" => Some("list"),
        _ => None,
    }
}

pub fn build_cli() -> Command {
    Command::new("nx")
        .about("Fast Node.js Package Manager")
        .version(env!("CARGO_PKG_VERSION"))
        .author("NX Package Manager Team")
        .long_about("NX is a fast Node.js package manager built with Rust.\nIt's designed to be a drop-in replacement for npm with improved performance.\n\nExamples:\n  nx install          # Install all dependencies from package.json\n  nx install lodash   # Install a specific package\n  nx i express        # Short form install\n  nx uninstall lodash # Remove a package\n  nx run start        # Run a script from package.json\n  nx list             # List installed packages")
        .before_help("NX - Node.js Package Manager\nFast, reliable npm alternative built with Rust")
        .subcommand(
            Command::new("install")
                .visible_alias("i")
                .about("Install packages")
                .long_about("Install packages from npm registry. If no package is specified, installs all dependencies from package.json.")
                .arg(
                    Arg::new("packages")
                        .help("Package names to install")
                        .long_help("One or more package names to install from npm registry")
                        .num_args(0..)
                        .value_name("PACKAGE"),
                )
                .arg(
                    Arg::new("save-dev")
                        .help("Save to devDependencies")
                        .long("save-dev")
                        .short('D')
                        .action(clap::ArgAction::SetTrue),
                )
                .arg(
                    Arg::new("force")
                        .help("Force reinstall packages")
                        .long("force")
                        .short('f')
                        .action(clap::ArgAction::SetTrue),
                ),
        )
        .subcommand(
            Command::new("uninstall")
                .visible_alias("remove")
                .visible_alias("rm")
                .about("Uninstall packages")
                .long_about("Remove packages from node_modules and update package.json.")
                .arg(
                    Arg::new("packages")
                        .help("Package names to uninstall")
                        .long_help("One or more package names to remove")
                        .required(true)
                        .num_args(1..)
                        .value_name("PACKAGE"),
                ),
        )
        .subcommand(
            Command::new("run")
                .about("Run npm scripts")
                .long_about("Execute scripts defined in package.json.")
                .arg(
                    Arg::new("script_and_args")
                        .help("Script name and optional arguments")
                        .long_help("The name of the script defined in package.json to execute, followed by optional arguments")
                        .required(true)
                        .num_args(1..)
                        .value_name("SCRIPT [ARGS]..."),
                ),
        )
        .subcommand(
            Command::new("list")
                .visible_alias("ls")
                .about("List installed packages")
                .long_about("Display all packages installed in node_modules with their versions."),
        )
        .subcommand(
            Command::new("init")
                .about("Initialize a new package.json")
                .long_about("Create a new package.json file with default values.")
                .arg(
                    Arg::new("yes")
                        .help("Skip interactive prompts and use defaults")
                        .long("yes")
                        .short('y')
                        .action(clap::ArgAction::SetTrue),
                ),
        )
        .subcommand(
            Command::new("start")
                .about("Start the application")
                .long_about("Run the 'start' script defined in package.json."),
        )
        .subcommand(
            Command::new("test")
                .about("Run tests")
                .long_about("Run the 'test' script defined in package.json."),
        )
        .subcommand(
            Command::new("dev")
                .about("Start development server")
                .long_about("Run the 'dev' script defined in package.json."),
        )
        .subcommand(
            Command::new("build")
                .about("Build the project")
                .long_about("Run the 'build' script defined in package.json."),
        )
        .subcommand(
            Command::new("update")
                .visible_alias("up")
                .about("Update packages")
                .long_about("Update packages to their latest versions within semver constraints.")
                .arg(
                    Arg::new("packages")
                        .help("Specific packages to update")
                        .num_args(0..)
                        .value_name("PACKAGE"),
                ),
        )
        .subcommand(
            Command::new("outdated")
                .about("Check for outdated packages")
                .long_about("Display packages that have newer versions available."),
        )
        .subcommand(
            Command::new("audit")
                .about("Audit packages for vulnerabilities")
                .long_about("Check installed packages for known security vulnerabilities.")
                .arg(
                    Arg::new("fix")
                        .help("Automatically fix vulnerabilities")
                        .long("fix")
                        .action(clap::ArgAction::SetTrue),
                ),
        )
        .subcommand(
            Command::new("clean")
                .about("Clean cache and temporary files")
                .long_about("Remove cache files and temporary data to free up space.")
                .arg(
                    Arg::new("all")
                        .help("Clean all caches including downloads")
                        .long("all")
                        .action(clap::ArgAction::SetTrue),
                ),
        )
        .subcommand(
            Command::new("info")
                .about("Show package information")
                .long_about("Display detailed information about a package.")
                .arg(
                    Arg::new("package")
                        .help("Package name to get information about")
                        .required(true)
                        .value_name("PACKAGE"),
                ),
        )
        .subcommand(
            Command::new("search")
                .about("Search for packages")
                .long_about("Search the npm registry for packages.")
                .arg(
                    Arg::new("query")
                        .help("Search query")
                        .required(true)
                        .value_name("QUERY"),
                )
                .arg(
                    Arg::new("limit")
                        .help("Limit number of results")
                        .long("limit")
                        .short('l')
                        .value_name("NUMBER")
                        .default_value("20"),
                ),
        )
        .subcommand(
            Command::new("workspace")
                .about("Workspace management")
                .long_about("Manage monorepo workspaces and multi-package projects.")
                .subcommand(
                    Command::new("list")
                        .about("List all workspaces")
                        .long_about("Display all workspaces in the current project."),
                )
                .subcommand(
                    Command::new("run")
                        .about("Run script in all workspaces")
                        .long_about("Execute a script in all workspace packages.")
                        .arg(
                            Arg::new("script")
                                .help("Script name to run")
                                .required(true)
                                .value_name("SCRIPT"),
                        ),
                ),
        )
}

pub async fn handle_matches(matches: &clap::ArgMatches) -> Result<(), Box<dyn std::error::Error>> {
    print_header();

    match matches.subcommand() {
        Some(("install", sub_matches)) => {
            let packages: Vec<String> = sub_matches
                .get_many::<String>("packages")
                .map(|vals| vals.cloned().collect())
                .unwrap_or_default();
            let save_dev = sub_matches.get_flag("save-dev");
            let force = sub_matches.get_flag("force");

            if packages.is_empty() {
                // Install all dependencies from package.json
                crate::installer::install_all(force).await?;
            } else {
                // Install specific packages
                crate::installer::install_packages(&packages, save_dev, force).await?;
            }
            Ok(())
        }
        Some(("uninstall", sub_matches)) => {
            let packages: Vec<String> = sub_matches
                .get_many::<String>("packages")
                .expect("Package names are required")
                .cloned()
                .collect();
            crate::installer::uninstall_packages(&packages).await?;
            Ok(())
        }
        Some(("run", sub_matches)) => {
            let script_and_args: Vec<String> = sub_matches
                .get_many::<String>("script_and_args")
                .expect("Script name is required")
                .cloned()
                .collect();

            let script = &script_and_args[0];
            let args = if script_and_args.len() > 1 {
                &script_and_args[1..]
            } else {
                &[]
            };

            crate::installer::run_script(script, args).await?;
            Ok(())
        }
        Some(("list", _)) => {
            crate::installer::list_packages().await?;
            Ok(())
        }
        Some(("init", sub_matches)) => {
            let yes = sub_matches.get_flag("yes");
            crate::installer::init_package_json(yes).await?;
            Ok(())
        }
        Some(("start", _)) => {
            crate::installer::run_script("start", &[]).await?;
            Ok(())
        }
        Some(("test", _)) => {
            crate::installer::run_script("test", &[]).await?;
            Ok(())
        }
        Some(("dev", _)) => {
            crate::installer::run_script("dev", &[]).await?;
            Ok(())
        }
        Some(("build", _)) => {
            crate::installer::run_script("build", &[]).await?;
            Ok(())
        }
        Some(("update", sub_matches)) => {
            let packages: Vec<String> = sub_matches
                .get_many::<String>("packages")
                .map(|vals| vals.cloned().collect())
                .unwrap_or_default();
            crate::installer::update_packages(&packages).await?;
            Ok(())
        }
        Some(("outdated", _)) => {
            crate::installer::check_outdated().await?;
            Ok(())
        }
        Some(("audit", sub_matches)) => {
            let fix = sub_matches.get_flag("fix");
            crate::installer::audit_packages(fix).await?;
            Ok(())
        }
        Some(("clean", sub_matches)) => {
            let all = sub_matches.get_flag("all");
            crate::installer::clean_cache(all).await?;
            Ok(())
        }
        Some(("info", sub_matches)) => {
            let package = sub_matches.get_one::<String>("package").expect("Package name is required");
            crate::installer::show_package_info(package).await?;
            Ok(())
        }
        Some(("search", sub_matches)) => {
            let query = sub_matches.get_one::<String>("query").expect("Search query is required");
            let limit: usize = sub_matches.get_one::<String>("limit")
                .unwrap_or(&"20".to_string())
                .parse()
                .unwrap_or(20);
            crate::installer::search_packages(query, limit).await?;
            Ok(())
        }
        Some(("workspace", sub_matches)) => {
            match sub_matches.subcommand() {
                Some(("list", _)) => {
                    crate::installer::list_workspaces().await?;
                }
                Some(("run", workspace_sub)) => {
                    let script = workspace_sub.get_one::<String>("script").expect("Script name is required");
                    crate::installer::run_workspace_script(script).await?;
                }
                _ => {
                    eprintln!("❌ Unknown workspace command. Run 'nx workspace --help' for available commands.");
                    std::process::exit(1);
                }
            }
            Ok(())
        }
        _ => {
            // Handle potential typos
            if let Some((cmd, _)) = matches.subcommand() {
                if let Some(suggestion) = suggest_command(cmd) {
                    eprintln!("❌ Unknown command '{}'. Did you mean '{}'?", cmd, suggestion);
                    eprintln!("   Run 'nx --help' for available commands.");
                } else {
                    eprintln!("❌ Unknown command '{}'. Run 'nx --help' for available commands.", cmd);
                }
            }
            std::process::exit(1);
        }
    }
}
