// Performance benchmarks for nx package manager

use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use std::time::Duration;
use tokio::runtime::Runtime;

// Benchmark network client performance
fn bench_network_client(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("network_client_fetch_package", |b| {
        b.to_async(&rt).iter(|| async {
            let client = nx::network::NetworkClient::global();
            let result = client.fetch_package_info(black_box("lodash")).await;
            black_box(result)
        })
    });
}

// Benchmark dependency resolution
fn bench_dependency_resolution(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("dependency_resolution");
    
    // Test with different numbers of packages
    for package_count in [1, 5, 10, 20].iter() {
        let packages: Vec<String> = (0..*package_count)
            .map(|i| format!("test-package-{}", i))
            .collect();
        
        group.bench_with_input(
            BenchmarkId::new("resolve_dependencies", package_count),
            &packages,
            |b, packages| {
                b.to_async(&rt).iter(|| async {
                    let installer = nx::installer::NpmInstaller::new().unwrap();
                    let result = installer.resolve_dependencies(black_box(packages)).await;
                    black_box(result)
                })
            },
        );
    }
    
    group.finish();
}

// Benchmark cache performance
fn bench_cache_performance(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("cache_hit_performance", |b| {
        b.to_async(&rt).iter(|| async {
            let client = nx::network::NetworkClient::global();
            
            // First request to populate cache
            let _ = client.fetch_package_info("express").await;
            
            // Benchmark cached request
            let result = client.fetch_package_info(black_box("express")).await;
            black_box(result)
        })
    });
}

// Benchmark parallel processing
fn bench_parallel_processing(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("parallel_processing");
    group.measurement_time(Duration::from_secs(30)); // Longer measurement time
    
    // Test with different concurrency levels
    for concurrency in [1, 5, 10, 20, 50].iter() {
        group.bench_with_input(
            BenchmarkId::new("parallel_downloads", concurrency),
            concurrency,
            |b, &concurrency| {
                b.to_async(&rt).iter(|| async {
                    let packages: Vec<nx::installer::NpmPackage> = (0..concurrency)
                        .map(|i| nx::installer::NpmPackage {
                            name: format!("test-package-{}", i),
                            version: "1.0.0".to_string(),
                            tarball_url: "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz".to_string(),
                            description: "Test package".to_string(),
                        })
                        .collect();
                    
                    let installer = nx::installer::NpmInstaller::new().unwrap();
                    let result = nx::installer::download_packages_parallel(
                        black_box(&packages), 
                        black_box(&installer)
                    ).await;
                    black_box(result)
                })
            },
        );
    }
    
    group.finish();
}

// Benchmark memory optimization
fn bench_memory_optimization(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("memory_mapped_extraction", |b| {
        b.to_async(&rt).iter(|| async {
            let extractor = nx::memory::create_memory_mapped_extractor().unwrap();
            let temp_dir = tempfile::tempdir().unwrap();
            
            // Simulate a large package (5MB of data)
            let large_data = vec![0u8; 5 * 1024 * 1024];
            
            let result = extractor.extract_large_package(
                black_box(&large_data),
                black_box(temp_dir.path())
            ).await;
            black_box(result)
        })
    });
}

// Benchmark configuration loading
fn bench_config_loading(c: &mut Criterion) {
    c.bench_function("config_loading", |b| {
        b.iter(|| {
            let config = nx::config::get_config();
            let validation = config.validate();
            black_box((config, validation))
        })
    });
}

// Benchmark error handling
fn bench_error_handling(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("error_handling", |b| {
        b.to_async(&rt).iter(|| async {
            let installer = nx::installer::NpmInstaller::new().unwrap();
            
            // Test error handling with non-existent package
            let result = installer.fetch_package_info(black_box("non-existent-package-12345")).await;
            black_box(result)
        })
    });
}

// Comprehensive benchmark comparing nx vs simulated npm performance
fn bench_nx_vs_npm_simulation(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("nx_vs_npm");
    group.measurement_time(Duration::from_secs(60)); // Longer measurement for accuracy
    
    let test_packages = vec![
        "lodash".to_string(),
        "moment".to_string(),
        "axios".to_string(),
        "chalk".to_string(),
    ];
    
    // Benchmark nx performance
    group.bench_function("nx_full_workflow", |b| {
        b.to_async(&rt).iter(|| async {
            let installer = nx::installer::NpmInstaller::new().unwrap();
            let resolved = installer.resolve_dependencies(black_box(&test_packages)).await;
            black_box(resolved)
        })
    });
    
    // Simulate npm performance (much slower)
    group.bench_function("npm_simulated", |b| {
        b.to_async(&rt).iter(|| async {
            // Simulate npm's slower performance with artificial delay
            tokio::time::sleep(Duration::from_millis(500)).await; // Simulate 500ms delay
            
            // Simulate some work
            let mut result = Vec::new();
            for package in black_box(&test_packages) {
                result.push(package.clone());
                tokio::time::sleep(Duration::from_millis(100)).await; // Per-package delay
            }
            black_box(result)
        })
    });
    
    group.finish();
}

// Benchmark streaming downloads
fn bench_streaming_downloads(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("streaming_download", |b| {
        b.to_async(&rt).iter(|| async {
            let downloader = nx::memory::create_streaming_downloader();
            
            // Create a mock response for testing
            // In a real benchmark, you'd use actual HTTP responses
            let client = reqwest::Client::new();
            let response = client.get("https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz")
                .send()
                .await
                .unwrap();
            
            let result = downloader.download_streaming(
                black_box(response),
                black_box(Some(100_000)) // 100KB estimated size
            ).await;
            black_box(result)
        })
    });
}

// Benchmark progress reporting overhead
fn bench_progress_reporting(c: &mut Criterion) {
    c.bench_function("progress_bar_updates", |b| {
        b.iter(|| {
            let mut progress = nx::progress::ProgressBar::new(1000, "benchmark");
            for i in 0..1000 {
                progress.update(black_box(i));
            }
            progress.finish();
        })
    });
}

// Real-world scenario benchmark
fn bench_real_world_scenario(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("real_world_scenarios");
    group.measurement_time(Duration::from_secs(120)); // 2 minutes for real-world test
    
    // Scenario 1: Installing a popular React app dependencies
    let react_packages = vec![
        "react".to_string(),
        "react-dom".to_string(),
        "react-router-dom".to_string(),
        "axios".to_string(),
        "styled-components".to_string(),
    ];
    
    group.bench_function("react_app_dependencies", |b| {
        b.to_async(&rt).iter(|| async {
            let installer = nx::installer::NpmInstaller::new().unwrap();
            let result = installer.resolve_dependencies(black_box(&react_packages)).await;
            black_box(result)
        })
    });
    
    // Scenario 2: Installing Node.js backend dependencies
    let node_packages = vec![
        "express".to_string(),
        "mongoose".to_string(),
        "jsonwebtoken".to_string(),
        "bcryptjs".to_string(),
        "cors".to_string(),
    ];
    
    group.bench_function("node_backend_dependencies", |b| {
        b.to_async(&rt).iter(|| async {
            let installer = nx::installer::NpmInstaller::new().unwrap();
            let result = installer.resolve_dependencies(black_box(&node_packages)).await;
            black_box(result)
        })
    });
    
    group.finish();
}

criterion_group!(
    benches,
    bench_network_client,
    bench_dependency_resolution,
    bench_cache_performance,
    bench_parallel_processing,
    bench_memory_optimization,
    bench_config_loading,
    bench_error_handling,
    bench_nx_vs_npm_simulation,
    bench_streaming_downloads,
    bench_progress_reporting,
    bench_real_world_scenario
);

criterion_main!(benches);
