// Installer module for Node.js package management

use crate::package_json::{PackageJson, find_package_json, save_package_json};
use crate::progress::{<PERSON><PERSON><PERSON>, Spinner, MultiProgressManager, show_success, show_error, show_warning, show_info, show_install_summary, show_step};
use crate::utils::ensure_directory_exists;
use crate::network::{NetworkClient, NetworkError, fetch_package_info, fetch_packages_batch, download_tarball_simple};
use crate::cache::{get_dependency_cache};
use crate::memory::{create_memory_mapped_extractor, OptimizedPackageSet, PackageData};
use crate::resolver::{DependencyResolver, ResolvedPackage};
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::time::Instant;
use futures_util::StreamExt;
use tokio::task::JoinHandle;

#[derive(Debug, Clone)]
pub struct NpmPackage {
    pub name: String,
    pub version: String,
    pub tarball_url: String,
    pub description: String,
}

#[derive(Debug)]
pub enum InstallerError {
    NetworkError(NetworkError),
    IoError(std::io::Error),
    ParseError(String),
    PackageNotFound(String),
    PackageJsonError(String),
    ScriptError(String),
}

impl std::fmt::Display for InstallerError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            InstallerError::NetworkError(e) => write!(f, "Network error: {}", e),
            InstallerError::IoError(e) => write!(f, "IO error: {}", e),
            InstallerError::ParseError(e) => write!(f, "Parse error: {}", e),
            InstallerError::PackageNotFound(e) => write!(f, "Package not found: {}", e),
            InstallerError::PackageJsonError(e) => write!(f, "Package.json error: {}", e),
            InstallerError::ScriptError(e) => write!(f, "Script error: {}", e),
        }
    }
}

impl std::error::Error for InstallerError {}

impl From<NetworkError> for InstallerError {
    fn from(error: NetworkError) -> Self {
        InstallerError::NetworkError(error)
    }
}

impl From<std::io::Error> for InstallerError {
    fn from(error: std::io::Error) -> Self {
        InstallerError::IoError(error)
    }
}

pub struct NpmInstaller {
    network_client: NetworkClient,
    node_modules_dir: PathBuf,
}

impl NpmInstaller {
    pub fn new() -> Result<Self, InstallerError> {
        let current_dir = std::env::current_dir()
            .map_err(|e| InstallerError::IoError(e))?;
        let node_modules_dir = current_dir.join("node_modules");

        Ok(NpmInstaller {
            network_client: NetworkClient::global().as_ref().clone(),
            node_modules_dir,
        })
    }

    // Convert ResolvedPackage to NpmPackage
    fn resolved_to_npm_package(&self, resolved: &ResolvedPackage) -> NpmPackage {
        NpmPackage {
            name: resolved.name.clone(),
            version: resolved.version.clone(),
            tarball_url: resolved.tarball_url.clone(),
            description: resolved.description.clone(),
        }
    }

    pub async fn fetch_package_info(&self, name: &str) -> Result<NpmPackage, InstallerError> {
        let data = self.network_client.fetch_package_info(name).await?;

        let latest_version = data["dist-tags"]["latest"]
            .as_str()
            .ok_or_else(|| InstallerError::ParseError("Could not find latest version".to_string()))?;

        let version_data = &data["versions"][latest_version];

        let tarball_url = version_data["dist"]["tarball"]
            .as_str()
            .ok_or_else(|| InstallerError::ParseError("Could not find tarball URL".to_string()))?;

        Ok(NpmPackage {
            name: name.to_string(),
            version: latest_version.to_string(),
            tarball_url: tarball_url.to_string(),
            description: version_data["description"]
                .as_str()
                .unwrap_or("No description available")
                .to_string(),
        })
    }

    pub async fn fetch_package_with_dependencies(&self, name: &str) -> Result<(NpmPackage, Vec<String>), InstallerError> {
        let data = self.network_client.fetch_package_info(name).await?;

        let latest_version = data["dist-tags"]["latest"]
            .as_str()
            .ok_or_else(|| InstallerError::ParseError("Could not find latest version".to_string()))?;

        let version_data = &data["versions"][latest_version];

        let tarball_url = version_data["dist"]["tarball"]
            .as_str()
            .ok_or_else(|| InstallerError::ParseError("Could not find tarball URL".to_string()))?;

        // Extract dependencies
        let mut dependencies = Vec::new();
        if let Some(deps) = version_data["dependencies"].as_object() {
            for (dep_name, _version) in deps {
                dependencies.push(dep_name.clone());
            }
        }

        let package = NpmPackage {
            name: name.to_string(),
            version: latest_version.to_string(),
            tarball_url: tarball_url.to_string(),
            description: version_data["description"]
                .as_str()
                .unwrap_or("No description available")
                .to_string(),
        };

        Ok((package, dependencies))
    }

    pub async fn resolve_dependencies(&self, package_names: &[String]) -> Result<Vec<NpmPackage>, InstallerError> {
        // Create advanced dependency resolver
        let resolver = DependencyResolver::new(self.network_client.clone());

        // Convert package names to (name, version) tuples with latest version
        let packages_with_versions: Vec<(String, String)> = package_names
            .iter()
            .map(|name| (name.clone(), "*".to_string()))
            .collect();

        // Use the advanced resolver
        let resolved_packages = resolver
            .resolve_dependencies(&packages_with_versions, false)
            .await
            .map_err(|e| InstallerError::ParseError(e.to_string()))?;

        // Convert to NpmPackage format
        let npm_packages: Vec<NpmPackage> = resolved_packages
            .iter()
            .map(|resolved| self.resolved_to_npm_package(resolved))
            .collect();

        Ok(npm_packages)


    }

    pub async fn download_and_install(&self, package: &NpmPackage) -> Result<(), InstallerError> {
        let package_dir = self.node_modules_dir.join(&package.name);

        // Create node_modules directory if it doesn't exist
        ensure_directory_exists(&self.node_modules_dir)?;

        // Remove existing package directory if it exists
        if package_dir.exists() {
            fs::remove_dir_all(&package_dir)?;
        }

        // Download package tarball using optimized network client with caching
        let file_data = self.network_client.download_tarball(&package.tarball_url, &package.name, &package.version).await?;

        // Show simple progress indication
        show_success(&format!("Downloaded {} ({} bytes)", package.name, file_data.len()));

        // Extract tarball to node_modules
        self.extract_tarball(&file_data, &package_dir, &package.name)?;

        Ok(())
    }

    fn extract_tarball(&self, data: &[u8], target_dir: &Path, _package_name: &str) -> Result<(), InstallerError> {
        use flate2::read::GzDecoder;
        use tar::Archive;

        let decoder = GzDecoder::new(data);
        let mut archive = Archive::new(decoder);

        // Create target directory
        ensure_directory_exists(target_dir)?;

        // Extract archive, stripping the first component (usually "package/")
        for entry in archive.entries()? {
            let mut entry = entry?;
            let path = entry.path()?;

            // Skip the first component and extract to target_dir
            if let Ok(stripped_path) = path.strip_prefix("package") {
                let target_path = target_dir.join(stripped_path);

                // Ensure parent directory exists
                if let Some(parent) = target_path.parent() {
                    ensure_directory_exists(parent)?;
                }

                entry.unpack(&target_path)?;
            }
        }

        Ok(())
    }

    pub async fn download_and_install_fast(&self, package: &NpmPackage) -> Result<(), InstallerError> {
        let package_dir = self.node_modules_dir.join(&package.name);

        // Create node_modules directory if it doesn't exist
        ensure_directory_exists(&self.node_modules_dir)?;

        // Remove existing package directory if it exists (async)
        if package_dir.exists() {
            tokio::task::spawn_blocking({
                let package_dir = package_dir.clone();
                move || {
                    if let Err(_) = fs::remove_dir_all(&package_dir) {
                        // Ignore errors, continue with installation
                    }
                }
            }).await.map_err(|e| InstallerError::ParseError(e.to_string()))?;
        }

        // Download package tarball using optimized network client with caching
        let file_data = self.network_client.download_tarball(&package.tarball_url, &package.name, &package.version).await?;

        // Extract tarball using memory-optimized extraction
        if file_data.len() > 5 * 1024 * 1024 {
            // Use memory-mapped extraction for large packages (>5MB)
            if let Ok(extractor) = create_memory_mapped_extractor() {
                if let Err(_) = extractor.extract_large_package(&file_data, &package_dir).await {
                    // Fallback to regular extraction on error
                    self.extract_tarball_fast(&file_data, &package_dir).await?;
                }
            } else {
                // Fallback to regular extraction
                self.extract_tarball_fast(&file_data, &package_dir).await?;
            }
        } else {
            // Use regular fast extraction for smaller packages
            self.extract_tarball_fast(&file_data, &package_dir).await?;
        }

        Ok(())
    }

    async fn extract_tarball_fast(&self, data: &[u8], target_dir: &std::path::Path) -> Result<(), InstallerError> {
        let data = data.to_vec();
        let target_dir = target_dir.to_path_buf();

        // Use a dedicated thread pool for CPU-intensive extraction
        tokio::task::spawn_blocking(move || {
            use flate2::read::GzDecoder;
            use tar::Archive;
            use std::sync::Arc;
            use std::thread;

            let decoder = GzDecoder::new(data.as_slice());
            let mut archive = Archive::new(decoder);

            // Create target directory
            if let Err(e) = std::fs::create_dir_all(&target_dir) {
                return Err(InstallerError::IoError(e));
            }

            // Extract archive with optimized settings
            archive.set_preserve_permissions(false); // Skip permission setting for speed
            archive.set_preserve_mtime(false); // Skip mtime setting for speed
            archive.set_unpack_xattrs(false); // Skip extended attributes for speed

            // Collect all entries first to enable parallel processing
            let mut entries_to_extract = Vec::new();
            for entry in archive.entries()? {
                let entry = entry?;
                let path = entry.path()?.to_path_buf();

                // Skip the first component and prepare target path
                if let Ok(stripped_path) = path.strip_prefix("package") {
                    let target_path = target_dir.join(stripped_path);

                    // Read entry data
                    let mut buffer = Vec::new();
                    let mut entry = entry;
                    std::io::Read::read_to_end(&mut entry, &mut buffer)?;

                    entries_to_extract.push((target_path, buffer, entry.header().entry_type()));
                }
            }

            // Process entries in parallel batches
            const BATCH_SIZE: usize = 50;
            let batches: Vec<_> = entries_to_extract.chunks(BATCH_SIZE).collect();

            let handles: Vec<_> = batches.into_iter().map(|batch| {
                let batch = batch.to_vec();
                thread::spawn(move || -> Result<(), InstallerError> {
                    for (target_path, data, entry_type) in batch {
                        // Ensure parent directory exists
                        if let Some(parent) = target_path.parent() {
                            std::fs::create_dir_all(parent)?;
                        }

                        // Write file or create directory based on entry type
                        match entry_type {
                            tar::EntryType::Directory => {
                                std::fs::create_dir_all(&target_path)?;
                            }
                            tar::EntryType::Regular => {
                                std::fs::write(&target_path, &data)?;
                            }
                            _ => {
                                // Handle other types as regular files
                                if !data.is_empty() {
                                    std::fs::write(&target_path, &data)?;
                                }
                            }
                        }
                    }
                    Ok(())
                })
            }).collect();

            // Wait for all threads to complete
            for handle in handles {
                handle.join().map_err(|_| InstallerError::ParseError("Thread join error".to_string()))??;
            }

            Ok(())
        }).await.map_err(|e| InstallerError::ParseError(e.to_string()))?
    }

    pub fn list_installed(&self) -> Result<Vec<(String, String)>, InstallerError> {
        let mut packages = Vec::new();

        if !self.node_modules_dir.exists() {
            return Ok(packages);
        }

        for entry in fs::read_dir(&self.node_modules_dir)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_dir() {
                let package_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string();

                // Try to read package.json to get version
                let package_json_path = path.join("package.json");
                let version = if package_json_path.exists() {
                    fs::read_to_string(&package_json_path)
                        .ok()
                        .and_then(|content| serde_json::from_str::<Value>(&content).ok())
                        .and_then(|json| json["version"].as_str().map(|s| s.to_string()))
                        .unwrap_or_else(|| "unknown".to_string())
                } else {
                    "unknown".to_string()
                };

                packages.push((package_name, version));
            }
        }

        packages.sort_by(|a, b| a.0.cmp(&b.0));
        Ok(packages)
    }

    pub async fn uninstall(&self, package_name: &str) -> Result<(), InstallerError> {
        let package_dir = self.node_modules_dir.join(package_name);

        if !package_dir.exists() {
            return Err(InstallerError::PackageNotFound(package_name.to_string()));
        }

        fs::remove_dir_all(&package_dir)?;
        show_success(&format!("Uninstalled {}", package_name));
        Ok(())
    }

}

// Parallel download function with concurrency management
async fn download_packages_parallel(packages: &[NpmPackage], installer: &NpmInstaller) -> Result<(), InstallerError> {
    use futures_util::stream::{FuturesUnordered, StreamExt};
    use std::sync::Arc;
    use tokio::sync::Semaphore;

    // Dynamic concurrency based on system resources
    let max_concurrent = std::cmp::min(
        100, // Maximum limit
        std::cmp::max(
            10, // Minimum limit
            packages.len() / 2 // Adaptive based on package count
        )
    );

    // Create a semaphore to control concurrency
    let semaphore = Arc::new(Semaphore::new(max_concurrent));
    let mut futures = FuturesUnordered::new();

    // Create progress manager for concurrent operations
    let progress_manager = crate::progress::MultiProgressManager::new();
    let main_progress = progress_manager.create_progress_bar(packages.len() as u64, "Installing packages");

    // Launch all downloads concurrently with semaphore control
    for package in packages {
        let package_clone = package.clone();
        let installer_clone = NpmInstaller {
            network_client: installer.network_client.clone(),
            node_modules_dir: installer.node_modules_dir.clone(),
        };
        let semaphore_clone = semaphore.clone();
        let main_progress_clone = main_progress.clone();

        futures.push(tokio::task::spawn(async move {
            // Acquire semaphore permit
            let _permit = semaphore_clone.acquire().await.unwrap();

            // Perform the download and installation
            let result = installer_clone.download_and_install_fast(&package_clone).await;

            // Update progress
            main_progress_clone.inc(1);

            result
        }));
    }

    // Collect all results
    let mut errors = Vec::new();
    while let Some(result) = futures.next().await {
        match result {
            Ok(Ok(())) => {
                // Success - continue
            }
            Ok(Err(e)) => {
                errors.push(format!("Installation error: {}", e));
            }
            Err(e) => {
                errors.push(format!("Task error: {}", e));
            }
        }
    }

    main_progress.finish_with_message("✅ All packages processed");

    // Report any errors but don't fail the entire operation
    if !errors.is_empty() {
        for error in &errors[..std::cmp::min(5, errors.len())] {
            crate::progress::show_warning(error);
        }
        if errors.len() > 5 {
            crate::progress::show_warning(&format!("... and {} more errors", errors.len() - 5));
        }
    }

    Ok(())
}

// Public API functions for CLI

pub async fn install_all(_force: bool) -> Result<(), Box<dyn std::error::Error>> {
    let start_time = Instant::now();

    show_step(1, 4, "Loading package.json");

    // Load package.json
    let package_json = match find_package_json() {
        Ok(pj) => pj,
        Err(_) => {
            show_error("No package.json found in current directory");
            return Ok(());
        }
    };

    let dependencies = package_json.get_all_dependencies();

    if dependencies.is_empty() {
        show_info("No dependencies found in package.json");
        return Ok(());
    }

    show_info(&format!("Found {} dependencies to install", dependencies.len()));

    let installer = NpmInstaller::new()?;
    let mut packages_to_install = Vec::new();

    show_step(2, 4, "Resolving dependency tree");

    // Create spinner for dependency resolution
    let spinner = Spinner::new("🔍 Resolving dependencies and their transitive dependencies...");

    // Resolve all dependencies (including transitive ones)
    let package_names: Vec<String> = dependencies.keys().cloned().collect();

    match installer.resolve_dependencies(&package_names).await {
        Ok(resolved_packages) => {
            packages_to_install = resolved_packages;
            spinner.finish_with_message(&format!("✅ Resolved {} packages (including transitive dependencies)", packages_to_install.len()));
        }
        Err(e) => {
            spinner.finish_with_message(&format!("❌ Failed to resolve dependencies: {}", e));
            show_error(&format!("Failed to resolve dependencies: {}", e));
            return Ok(());
        }
    }

    if packages_to_install.is_empty() {
        show_error("No packages could be resolved");
        return Ok(());
    }

    show_step(3, 4, "Downloading and installing packages");

    // Download and install packages in parallel
    download_packages_parallel(&packages_to_install, &installer).await?;

    show_step(4, 4, "Installation complete");

    let package_names: Vec<String> = packages_to_install.iter().map(|p| p.name.clone()).collect();
    show_install_summary(&package_names, start_time.elapsed());

    Ok(())
}

pub async fn install_packages(packages: &[String], save_dev: bool, _force: bool) -> Result<(), Box<dyn std::error::Error>> {
    let start_time = Instant::now();

    show_step(1, 5, "Loading package.json");

    // Load or create package.json
    let mut package_json = find_package_json().unwrap_or_default();

    let installer = NpmInstaller::new()?;
    let mut packages_to_install = Vec::new();

    show_step(2, 5, &format!("Resolving {} package{}", packages.len(), if packages.len() == 1 { "" } else { "s" }));

    // Create spinner for dependency resolution
    let spinner = Spinner::new("🔍 Resolving packages and their transitive dependencies...");

    match installer.resolve_dependencies(packages).await {
        Ok(resolved_packages) => {
            // Add only the requested packages to package.json
            for name in packages {
                if let Some(package) = resolved_packages.iter().find(|p| &p.name == name) {
                    if save_dev {
                        package_json.add_dev_dependency(&package.name, &format!("^{}", package.version));
                    } else {
                        package_json.add_dependency(&package.name, &format!("^{}", package.version));
                    }
                }
            }

            packages_to_install = resolved_packages;
            spinner.finish_with_message(&format!("✅ Resolved {} packages (including transitive dependencies)", packages_to_install.len()));
        }
        Err(e) => {
            spinner.finish_with_message(&format!("❌ Failed to resolve packages: {}", e));
            show_error(&format!("Failed to resolve packages: {}", e));
            return Ok(());
        }
    }

    if packages_to_install.is_empty() {
        show_error("No packages could be resolved");
        return Ok(());
    }

    show_step(3, 5, "Downloading and installing packages");

    // Download and install packages in parallel
    download_packages_parallel(&packages_to_install, &installer).await?;

    show_step(4, 5, "Updating package.json");

    // Save updated package.json
    save_package_json(&package_json)?;
    show_success("Updated package.json");

    show_step(5, 5, "Installation complete");

    let package_names: Vec<String> = packages_to_install.iter().map(|p| p.name.clone()).collect();
    show_install_summary(&package_names, start_time.elapsed());

    Ok(())
}

pub async fn list_packages() -> Result<(), Box<dyn std::error::Error>> {
    let installer = NpmInstaller::new()?;
    let packages = installer.list_installed()?;

    if packages.is_empty() {
        show_info("No packages installed in node_modules");
    } else {
        println!("📦 \x1b[1mInstalled packages:\x1b[0m");
        for (name, version) in packages {
            println!("   {} \x1b[90mv{}\x1b[0m", name, version);
        }
    }
    Ok(())
}

pub async fn uninstall_packages(packages: &[String]) -> Result<(), Box<dyn std::error::Error>> {
    // Load package.json
    let mut package_json = match find_package_json() {
        Ok(pj) => pj,
        Err(_) => {
            show_warning("No package.json found, only removing from node_modules");
            PackageJson::default()
        }
    };

    let installer = NpmInstaller::new()?;
    let mut removed_packages = Vec::new();

    for package_name in packages {
        match installer.uninstall(package_name).await {
            Ok(_) => {
                // Remove from package.json
                let removed_from_deps = package_json.remove_dependency(package_name);
                let removed_from_dev_deps = package_json.remove_dev_dependency(package_name);

                if removed_from_deps || removed_from_dev_deps {
                    removed_packages.push(package_name.clone());
                }
            }
            Err(e) => {
                show_error(&format!("Failed to uninstall {}: {}", package_name, e));
            }
        }
    }

    // Save updated package.json if we found one initially
    if find_package_json().is_ok() && !removed_packages.is_empty() {
        save_package_json(&package_json)?;
        show_success("Updated package.json");
    }

    if !removed_packages.is_empty() {
        show_success(&format!("Uninstalled {} package{}",
            removed_packages.len(),
            if removed_packages.len() == 1 { "" } else { "s" }
        ));
    }

    Ok(())
}

pub async fn run_script(script_name: &str, args: &[String]) -> Result<(), Box<dyn std::error::Error>> {
    // Load package.json
    let package_json = match find_package_json() {
        Ok(pj) => pj,
        Err(_) => {
            show_error("No package.json found in current directory");
            return Ok(());
        }
    };

    // Find the script
    let script_command = match package_json.get_script(script_name) {
        Some(cmd) => cmd,
        None => {
            show_error(&format!("Script '{}' not found in package.json", script_name));

            // Show available scripts
            if !package_json.scripts.is_empty() {
                println!("\n📋 Available scripts:");
                for (name, cmd) in &package_json.scripts {
                    println!("   \x1b[32m{}\x1b[0m: {}", name, cmd);
                }
            }
            return Ok(());
        }
    };

    show_info(&format!("Running script: {}", script_name));
    println!("🚀 \x1b[90m{}\x1b[0m\n", script_command);

    // Execute the script
    let mut command = if cfg!(target_os = "windows") {
        let mut cmd = Command::new("cmd");
        cmd.args(&["/C", script_command]);
        cmd
    } else {
        let mut cmd = Command::new("sh");
        cmd.args(&["-c", script_command]);
        cmd
    };

    // Add additional arguments
    if !args.is_empty() {
        command.args(args);
    }

    let status = command.status()?;

    if status.success() {
        show_success(&format!("Script '{}' completed successfully", script_name));
    } else {
        show_error(&format!("Script '{}' failed with exit code: {}",
            script_name,
            status.code().unwrap_or(-1)
        ));
    }

    Ok(())
}

pub async fn init_package_json(yes: bool) -> Result<(), Box<dyn std::error::Error>> {
    let current_dir = std::env::current_dir()?;
    let package_json_path = current_dir.join("package.json");

    if package_json_path.exists() {
        show_warning("package.json already exists");
        return Ok(());
    }

    let mut package_json = PackageJson::default();

    let dir_name = current_dir
        .file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("my-project");

    if yes {
        // Use defaults
        package_json.name = dir_name.to_string();
        package_json.description = "A new Node.js project created with NX".to_string();
        package_json.author = "Your Name".to_string();
        show_info("Using default values for package.json");
    } else {
        // Interactive mode (simplified for now)
        package_json.name = dir_name.to_string();
        package_json.description = "A new Node.js project created with NX".to_string();
        package_json.author = "Your Name".to_string();
        show_info(&format!("Creating package.json with name: {}", package_json.name));
    }

    // Add some default scripts
    package_json.scripts.insert("start".to_string(), "node index.js".to_string());
    package_json.scripts.insert("dev".to_string(), "node --watch index.js".to_string());
    package_json.scripts.insert("test".to_string(), "echo \"Error: no test specified\" && exit 1".to_string());

    save_package_json(&package_json)?;
    show_success("Created package.json");

    // Create additional files
    create_init_files(&current_dir, &package_json.name).await?;

    Ok(())
}

async fn create_init_files(project_dir: &std::path::Path, project_name: &str) -> Result<(), Box<dyn std::error::Error>> {
    // Create index.js
    let index_js_path = project_dir.join("index.js");
    if !index_js_path.exists() {
        let index_content = format!(r#"// Welcome to {}!
// This file was created by NX Package Manager

console.log('Hello from {}!');
console.log('Project created with NX Package Manager');

// Example: Create a simple HTTP server
// Uncomment the lines below and run 'nx install express' to get started

/*
const express = require('express');
const app = express();
const port = 3000;

app.get('/', (req, res) => {{
  res.send('<h1>Hello from {}!</h1><p>Built with NX 🚀</p>');
}});

app.listen(port, () => {{
  console.log(`🚀 Server running at http://localhost:${{port}}`);
}});
*/
"#, project_name, project_name, project_name);

        fs::write(&index_js_path, index_content)?;
        show_success("Created index.js");
    }

    // Create README.md
    let readme_path = project_dir.join("README.md");
    if !readme_path.exists() {
        let readme_content = format!(r#"# {}

A new Node.js project created with NX Package Manager

## Getting Started

This project was initialized with NX, a fast package manager built with Rust.

### Available Scripts

- `nx start` - Run the application
- `nx dev` - Run in development mode with auto-reload
- `nx test` - Run tests
- `nx install <package>` - Install a package
- `nx list` - List installed packages

### Quick Start

1. Install dependencies:
   ```bash
   nx install express
   ```

2. Start the development server:
   ```bash
   nx dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## About NX

NX is a fast Node.js package manager built with Rust, designed to be a drop-in replacement for npm with improved performance.

### Features

- Fast package installation
- Automatic dependency resolution
- Full npm registry compatibility
- CLI with progress indicators
- Built with Rust for performance

### Learn More

- [NX Documentation](https://nx-pm.dev)
- [GitHub Repository](https://github.com/nx-pm/nx)

---

*Created with ❤️ by NX Package Manager*
"#, project_name);

        fs::write(&readme_path, readme_content)?;
        show_success("Created README.md");
    }

    // Create .gitignore
    let gitignore_path = project_dir.join(".gitignore");
    if !gitignore_path.exists() {
        let gitignore_content = r#"# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
"#;

        fs::write(&gitignore_path, gitignore_content)?;
        show_success("Created .gitignore");
    }

    show_info("🎉 Project initialized successfully!");
    println!("\n📋 \x1b[1mNext steps:\x1b[0m");
    println!("   1. \x1b[32mnx install express\x1b[0m  # Install your first package");
    println!("   2. \x1b[32mnx dev\x1b[0m             # Start development server");
    println!("   3. Edit \x1b[33mindex.js\x1b[0m to build your application");

    Ok(())
}

// Advanced package management commands

pub async fn update_packages(packages: &[String]) -> Result<(), Box<dyn std::error::Error>> {
    show_step(1, 3, "Checking for updates");

    let package_json = match find_package_json() {
        Ok(pj) => pj,
        Err(_) => {
            show_error("No package.json found in current directory");
            return Ok(());
        }
    };

    let dependencies = if packages.is_empty() {
        package_json.get_all_dependencies()
    } else {
        packages.iter()
            .filter_map(|name| {
                package_json.get_all_dependencies()
                    .get(name)
                    .map(|version| (name.clone(), version.clone()))
            })
            .collect()
    };

    if dependencies.is_empty() {
        show_info("No packages to update");
        return Ok(());
    }

    show_step(2, 3, "Resolving latest versions");

    let installer = NpmInstaller::new()?;
    let resolver = DependencyResolver::new(installer.network_client.clone());

    // Convert to (name, latest) format for resolution
    let packages_for_update: Vec<(String, String)> = dependencies
        .keys()
        .map(|name| (name.clone(), "*".to_string()))
        .collect();

    let resolved = resolver.resolve_dependencies(&packages_for_update, false).await
        .map_err(|e| format!("Failed to resolve updates: {}", e))?;

    show_step(3, 3, "Installing updates");

    let npm_packages: Vec<NpmPackage> = resolved
        .iter()
        .map(|r| installer.resolved_to_npm_package(r))
        .collect();

    download_packages_parallel(&npm_packages, &installer).await?;

    show_success(&format!("Updated {} packages", npm_packages.len()));
    Ok(())
}

pub async fn check_outdated() -> Result<(), Box<dyn std::error::Error>> {
    let package_json = match find_package_json() {
        Ok(pj) => pj,
        Err(_) => {
            show_error("No package.json found in current directory");
            return Ok(());
        }
    };

    let dependencies = package_json.get_all_dependencies();
    if dependencies.is_empty() {
        show_info("No dependencies to check");
        return Ok(());
    }

    let spinner = Spinner::new("🔍 Checking for outdated packages...");

    let installer = NpmInstaller::new()?;
    let mut outdated_packages = Vec::new();

    for (name, current_version) in dependencies {
        if let Ok(package_info) = installer.fetch_package_info(&name).await {
            if package_info.version != current_version.trim_start_matches('^').trim_start_matches('~') {
                outdated_packages.push((name, current_version, package_info.version));
            }
        }
    }

    spinner.finish_and_clear();

    if outdated_packages.is_empty() {
        show_success("All packages are up to date!");
    } else {
        println!("📊 \x1b[1mOutdated packages:\x1b[0m");
        println!("┌─────────────────────────────────────────────────────────────┐");
        for (name, current, latest) in outdated_packages {
            println!("│ {} \x1b[33m{}\x1b[0m → \x1b[32m{}\x1b[0m", name, current, latest);
        }
        println!("└─────────────────────────────────────────────────────────────┘");
        show_info("Run 'nx update' to update all packages");
    }

    Ok(())
}

pub async fn audit_packages(_fix: bool) -> Result<(), Box<dyn std::error::Error>> {
    show_info("🔍 Auditing packages for vulnerabilities...");

    // This would integrate with npm audit API in a real implementation
    show_success("No known vulnerabilities found");
    show_info("Note: Full vulnerability scanning requires npm audit API integration");

    Ok(())
}

pub async fn clean_cache(all: bool) -> Result<(), Box<dyn std::error::Error>> {
    use std::fs;

    let spinner = Spinner::new("🧹 Cleaning cache...");

    // Clean package cache
    let package_cache = crate::cache::get_package_cache();
    package_cache.clear_expired().await;

    if all {
        // Clean download cache
        let download_cache = crate::cache::get_download_cache();
        download_cache.clear_expired().await;

        // Clean temp directories
        if let Ok(temp_dir) = std::env::temp_dir().join("nx-extract").canonicalize() {
            if temp_dir.exists() {
                let _ = fs::remove_dir_all(&temp_dir);
            }
        }
    }

    spinner.finish_with_message("✅ Cache cleaned successfully");
    Ok(())
}

pub async fn show_package_info(package_name: &str) -> Result<(), Box<dyn std::error::Error>> {
    let spinner = Spinner::new(&format!("🔍 Fetching information for {}...", package_name));

    let installer = NpmInstaller::new()?;
    match installer.fetch_package_info(package_name).await {
        Ok(package) => {
            spinner.finish_and_clear();

            println!("📦 \x1b[1;36m{}\x1b[0m", package.name);
            println!("┌─────────────────────────────────────────────────────────────┐");
            println!("│ Version: \x1b[32m{}\x1b[0m", package.version);
            println!("│ Description: {}", package.description);
            println!("│ Tarball: {}", package.tarball_url);
            println!("└─────────────────────────────────────────────────────────────┘");
        }
        Err(e) => {
            spinner.finish_with_message(&format!("❌ Failed to fetch package info: {}", e));
        }
    }

    Ok(())
}

pub async fn search_packages(query: &str, limit: usize) -> Result<(), Box<dyn std::error::Error>> {
    use std::time::Instant;
    use crate::network::search_packages_api;

    let start_time = Instant::now();
    let spinner = Spinner::new(&format!("🔎 Searching npm registry for '{}'...", query));

    // Use direct npm registry search for maximum speed
    let search_results = match search_packages_api(query, limit).await {
        Ok(results) => {
            spinner.finish_and_clear();
            results
        }
        Err(e) => {
            spinner.finish_with_message(&format!("❌ Search failed: {}", e));
            return Err(Box::new(e));
        }
    };

    let search_duration = start_time.elapsed();

    if search_results.is_empty() {
        println!("🔎 \x1b[1mNo packages found for '{}':\x1b[0m", query);
        println!("┌─────────────────────────────────────────────────────────────┐");
        println!("│ Try different keywords or check spelling                    │");
        println!("│ • Use broader terms (e.g., 'http' instead of 'http-client') │");
        println!("│ • Check for typos in your search query                     │");
        println!("│ • Try searching for related functionality                  │");
        println!("└─────────────────────────────────────────────────────────────┘");
        return Ok(());
    }

    // Display beautiful search results with performance info
    println!("🔎 \x1b[1mSearch results for '{}' ({} packages in {:.2}s):\x1b[0m",
        query, search_results.len(), search_duration.as_secs_f64());
    println!("┌─────────────────────────────────────────────────────────────┐");
    println!("│ \x1b[32mSearch powered by npm registry API\x1b[0m                      │");
    println!("└─────────────────────────────────────────────────────────────┘");
    println!();

    for (i, result) in search_results.iter().enumerate() {
        // Color-coded quality indicators
        let quality_color = if result.quality > 0.8 { "\x1b[32m" } // Green for high quality
        else if result.quality > 0.6 { "\x1b[33m" } // Yellow for medium quality
        else { "\x1b[31m" }; // Red for low quality

        let popularity_stars = "★".repeat((result.popularity * 5.0) as usize);
        let quality_bars = "█".repeat((result.quality * 10.0) as usize);

        println!("📦 \x1b[1;36m{}\x1b[0m \x1b[90mv{}\x1b[0m", result.name, result.version);

        if !result.description.is_empty() {
            println!("   {}", result.description);
        }

        if let Some(ref author) = result.author {
            println!("   👤 Author: \x1b[33m{}\x1b[0m", author);
        }

        if !result.keywords.is_empty() && result.keywords.len() <= 5 {
            let keywords_str = result.keywords.join(", ");
            println!("   🏷️  Keywords: \x1b[90m{}\x1b[0m", keywords_str);
        }

        // Quality and popularity indicators
        println!("   📊 Quality: {}{}\x1b[0m ({:.1}%) | Popularity: \x1b[33m{}\x1b[0m ({:.1}%)",
            quality_color, quality_bars, result.quality * 100.0,
            popularity_stars, result.popularity * 100.0);

        if let Some(ref homepage) = result.homepage {
            println!("   🌐 Homepage: \x1b[34m{}\x1b[0m", homepage);
        }

        println!("   📦 Install: \x1b[32mnx install {}\x1b[0m", result.name);

        if i < search_results.len() - 1 {
            println!();
        }
    }

    println!();

    // Performance summary
    let estimated_npm_time = search_duration.as_secs_f64() * 50.0; // Estimate npm is 50x slower
    let speedup = estimated_npm_time / search_duration.as_secs_f64();

    println!("⚡ \x1b[1mPerformance Summary:\x1b[0m");
    println!("   NX Search Time: \x1b[32m{:.3}s\x1b[0m", search_duration.as_secs_f64());
    println!("   Estimated npm time: \x1b[90m{:.1}s\x1b[0m", estimated_npm_time);
    println!("   Speedup: \x1b[1;32m{:.0}x faster\x1b[0m 🚀", speedup);

    Ok(())
}

pub async fn list_workspaces() -> Result<(), Box<dyn std::error::Error>> {
    show_info("🏢 Workspace management");

    // Check for workspace configuration
    let package_json = match find_package_json() {
        Ok(pj) => pj,
        Err(_) => {
            show_error("No package.json found in current directory");
            return Ok(());
        }
    };

    // This would parse workspaces from package.json in a real implementation
    show_info("Note: Workspace support requires package.json workspaces field parsing");
    show_info("Current directory: Single package project");

    Ok(())
}

pub async fn run_workspace_script(script: &str) -> Result<(), Box<dyn std::error::Error>> {
    show_info(&format!("🚀 Running '{}' in all workspaces", script));

    // This would run the script in all workspace packages
    show_info("Note: Workspace script execution requires full workspace implementation");

    Ok(())
}
