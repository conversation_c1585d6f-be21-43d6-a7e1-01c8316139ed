{"rustc": 1842507548689473721, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 15625788057656754760, "path": 11971606486915890958, "deps": [[5157631553186200874, "num_traits", false, 3892753693862540571], [9689903380558560274, "serde", false, 9689167156444042997], [11505586985402185701, "windows_link", false, 6912170987237577325]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\chrono-9edde63c7d516a56\\dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}