# Rust
/target/
Cargo.lock
*.pdb

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Cache
.cache/
cache/

# Test artifacts
test-results/
coverage/
*.profraw

# Temporary files
*.tmp
*.temp
temp/

# Build artifacts
dist/
build/

# Node.js (for testing compatibility)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Environment
.env
.env.local
.env.*.local

# Benchmarks
benches/results/
criterion/

# Documentation
/docs/book/
/docs/target/

# Release artifacts
releases/
*.tar.gz
*.zip

# Development scripts
scripts/local/
scripts/dev/

# Test fixtures
tests/fixtures/large/
tests/fixtures/generated/
