{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 11211203357094538046, "path": 1120524010192533516, "deps": [[5820056977320921005, "anstream", false, 10311519334955956544], [9394696648929125047, "anstyle", false, 4772440942977527499], [11166530783118767604, "strsim", false, 6166815582009679108], [11649982696571033535, "clap_lex", false, 4298493588311097978]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_builder-17d32fa52f4f2896\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}