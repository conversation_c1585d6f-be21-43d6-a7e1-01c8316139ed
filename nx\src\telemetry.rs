// Telemetry and monitoring system for production deployment

use crate::config::get_config;
use crate::errors::{NxError, NxResult};
use serde::{Serialize, Deserialize};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use std::sync::Arc;
use tokio::sync::Mutex;
use once_cell::sync::Lazy;

// Global telemetry instance
static TELEMETRY: Lazy<Arc<TelemetryManager>> = Lazy::new(|| {
    Arc::new(TelemetryManager::new())
});

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TelemetryEvent {
    pub event_type: String,
    pub timestamp: u64,
    pub duration_ms: Option<u64>,
    pub success: bool,
    pub error_type: Option<String>,
    pub metadata: std::collections::HashMap<String, String>,
    pub session_id: String,
    pub version: String,
    pub platform: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub operation: String,
    pub duration_ms: u64,
    pub memory_usage_mb: Option<f64>,
    pub network_requests: u32,
    pub cache_hits: u32,
    pub cache_misses: u32,
    pub packages_processed: u32,
    pub concurrent_operations: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrashReport {
    pub timestamp: u64,
    pub version: String,
    pub platform: String,
    pub error_message: String,
    pub stack_trace: Option<String>,
    pub operation: String,
    pub session_id: String,
    pub system_info: SystemInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub os: String,
    pub arch: String,
    pub cpu_cores: u32,
    pub total_memory_mb: u64,
    pub available_memory_mb: u64,
    pub disk_space_mb: u64,
    pub rust_version: String,
}

pub struct TelemetryManager {
    session_id: String,
    events: Arc<Mutex<Vec<TelemetryEvent>>>,
    metrics: Arc<Mutex<Vec<PerformanceMetrics>>>,
    enabled: bool,
}

impl TelemetryManager {
    pub fn new() -> Self {
        let config = get_config();
        let session_id = uuid::Uuid::new_v4().to_string();
        
        Self {
            session_id,
            events: Arc::new(Mutex::new(Vec::new())),
            metrics: Arc::new(Mutex::new(Vec::new())),
            enabled: !config.ui.quiet, // Disable telemetry in quiet mode
        }
    }

    pub async fn record_event(&self, event_type: &str, success: bool, duration: Option<Duration>) {
        if !self.enabled {
            return;
        }

        let event = TelemetryEvent {
            event_type: event_type.to_string(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            duration_ms: duration.map(|d| d.as_millis() as u64),
            success,
            error_type: None,
            metadata: std::collections::HashMap::new(),
            session_id: self.session_id.clone(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            platform: get_platform_info(),
        };

        let mut events = self.events.lock().await;
        events.push(event);

        // Keep only last 1000 events to prevent memory bloat
        if events.len() > 1000 {
            events.drain(0..events.len() - 1000);
        }
    }

    pub async fn record_error(&self, operation: &str, error: &NxError) {
        if !self.enabled {
            return;
        }

        let event = TelemetryEvent {
            event_type: "error".to_string(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            duration_ms: None,
            success: false,
            error_type: Some(format!("{:?}", error)),
            metadata: {
                let mut map = std::collections::HashMap::new();
                map.insert("operation".to_string(), operation.to_string());
                map.insert("error_message".to_string(), error.to_string());
                map
            },
            session_id: self.session_id.clone(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            platform: get_platform_info(),
        };

        let mut events = self.events.lock().await;
        events.push(event);
    }

    pub async fn record_performance(&self, metrics: PerformanceMetrics) {
        if !self.enabled {
            return;
        }

        let mut perf_metrics = self.metrics.lock().await;
        perf_metrics.push(metrics);

        // Keep only last 500 metrics
        if perf_metrics.len() > 500 {
            perf_metrics.drain(0..perf_metrics.len() - 500);
        }
    }

    pub async fn record_crash(&self, operation: &str, error: &str, stack_trace: Option<String>) {
        if !self.enabled {
            return;
        }

        let crash_report = CrashReport {
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            platform: get_platform_info(),
            error_message: error.to_string(),
            stack_trace,
            operation: operation.to_string(),
            session_id: self.session_id.clone(),
            system_info: get_system_info(),
        };

        // In a real implementation, this would send to a crash reporting service
        self.save_crash_report(&crash_report).await;
    }

    async fn save_crash_report(&self, report: &CrashReport) {
        // Save crash report to local file for debugging
        let crash_dir = std::env::temp_dir().join("nx-crashes");
        if let Err(_) = std::fs::create_dir_all(&crash_dir) {
            return; // Silently fail if we can't create crash directory
        }

        let filename = format!("crash-{}-{}.json", report.timestamp, report.session_id);
        let filepath = crash_dir.join(filename);

        if let Ok(json) = serde_json::to_string_pretty(report) {
            let _ = std::fs::write(filepath, json);
        }
    }

    pub async fn flush_telemetry(&self) -> NxResult<()> {
        if !self.enabled {
            return Ok(());
        }

        // In a real implementation, this would send telemetry to a service
        // For now, we'll save to a local file for debugging
        let telemetry_dir = std::env::temp_dir().join("nx-telemetry");
        std::fs::create_dir_all(&telemetry_dir)
            .map_err(|e| NxError::io(format!("Failed to create telemetry directory: {}", e)))?;

        // Save events
        let events = self.events.lock().await;
        if !events.is_empty() {
            let filename = format!("events-{}.json", self.session_id);
            let filepath = telemetry_dir.join(filename);
            let json = serde_json::to_string_pretty(&*events)
                .map_err(|e| NxError::parse(format!("Failed to serialize events: {}", e)))?;
            std::fs::write(filepath, json)
                .map_err(|e| NxError::io(format!("Failed to write events: {}", e)))?;
        }

        // Save metrics
        let metrics = self.metrics.lock().await;
        if !metrics.is_empty() {
            let filename = format!("metrics-{}.json", self.session_id);
            let filepath = telemetry_dir.join(filename);
            let json = serde_json::to_string_pretty(&*metrics)
                .map_err(|e| NxError::parse(format!("Failed to serialize metrics: {}", e)))?;
            std::fs::write(filepath, json)
                .map_err(|e| NxError::io(format!("Failed to write metrics: {}", e)))?;
        }

        Ok(())
    }

    pub async fn get_session_summary(&self) -> SessionSummary {
        let events = self.events.lock().await;
        let metrics = self.metrics.lock().await;

        let total_events = events.len();
        let successful_events = events.iter().filter(|e| e.success).count();
        let error_events = events.iter().filter(|e| !e.success).count();

        let avg_duration = if !metrics.is_empty() {
            metrics.iter().map(|m| m.duration_ms).sum::<u64>() / metrics.len() as u64
        } else {
            0
        };

        let total_packages = metrics.iter().map(|m| m.packages_processed).sum::<u32>();
        let total_cache_hits = metrics.iter().map(|m| m.cache_hits).sum::<u32>();
        let total_cache_misses = metrics.iter().map(|m| m.cache_misses).sum::<u32>();

        SessionSummary {
            session_id: self.session_id.clone(),
            total_events,
            successful_events,
            error_events,
            avg_duration_ms: avg_duration,
            total_packages_processed: total_packages,
            cache_hit_rate: if total_cache_hits + total_cache_misses > 0 {
                (total_cache_hits as f64 / (total_cache_hits + total_cache_misses) as f64) * 100.0
            } else {
                0.0
            },
        }
    }
}

#[derive(Debug, Serialize)]
pub struct SessionSummary {
    pub session_id: String,
    pub total_events: usize,
    pub successful_events: usize,
    pub error_events: usize,
    pub avg_duration_ms: u64,
    pub total_packages_processed: u32,
    pub cache_hit_rate: f64,
}

// Performance monitoring utilities
pub struct PerformanceMonitor {
    operation: String,
    start_time: std::time::Instant,
    network_requests: u32,
    cache_hits: u32,
    cache_misses: u32,
    packages_processed: u32,
}

impl PerformanceMonitor {
    pub fn new(operation: &str) -> Self {
        Self {
            operation: operation.to_string(),
            start_time: std::time::Instant::now(),
            network_requests: 0,
            cache_hits: 0,
            cache_misses: 0,
            packages_processed: 0,
        }
    }

    pub fn record_network_request(&mut self) {
        self.network_requests += 1;
    }

    pub fn record_cache_hit(&mut self) {
        self.cache_hits += 1;
    }

    pub fn record_cache_miss(&mut self) {
        self.cache_misses += 1;
    }

    pub fn record_package_processed(&mut self) {
        self.packages_processed += 1;
    }

    pub async fn finish(self) {
        let duration = self.start_time.elapsed();
        let memory_usage = get_memory_usage();

        let metrics = PerformanceMetrics {
            operation: self.operation,
            duration_ms: duration.as_millis() as u64,
            memory_usage_mb: memory_usage,
            network_requests: self.network_requests,
            cache_hits: self.cache_hits,
            cache_misses: self.cache_misses,
            packages_processed: self.packages_processed,
            concurrent_operations: 1, // Could be enhanced to track actual concurrency
        };

        get_telemetry().record_performance(metrics).await;
    }
}

// Utility functions
fn get_platform_info() -> String {
    format!("{}-{}", std::env::consts::OS, std::env::consts::ARCH)
}

fn get_system_info() -> SystemInfo {
    SystemInfo {
        os: std::env::consts::OS.to_string(),
        arch: std::env::consts::ARCH.to_string(),
        cpu_cores: num_cpus::get() as u32,
        total_memory_mb: get_total_memory(),
        available_memory_mb: get_available_memory(),
        disk_space_mb: get_disk_space(),
        rust_version: rustc_version_runtime::version().to_string(),
    }
}

fn get_memory_usage() -> Option<f64> {
    // Simplified memory usage detection
    // In a real implementation, you'd use platform-specific APIs
    None
}

fn get_total_memory() -> u64 {
    // Placeholder - would use platform-specific APIs
    8192 // 8GB default
}

fn get_available_memory() -> u64 {
    // Placeholder - would use platform-specific APIs
    4096 // 4GB default
}

fn get_disk_space() -> u64 {
    // Placeholder - would check actual disk space
    if let Ok(current_dir) = std::env::current_dir() {
        if let Ok(metadata) = std::fs::metadata(&current_dir) {
            return metadata.len() / 1024 / 1024; // Convert to MB
        }
    }
    1024 // 1GB default
}

// Global access functions
pub fn get_telemetry() -> &'static TelemetryManager {
    &TELEMETRY
}

pub async fn record_operation_start(operation: &str) {
    get_telemetry().record_event(operation, true, None).await;
}

pub async fn record_operation_success(operation: &str, duration: Duration) {
    get_telemetry().record_event(operation, true, Some(duration)).await;
}

pub async fn record_operation_error(operation: &str, error: &NxError) {
    get_telemetry().record_error(operation, error).await;
}

pub async fn flush_telemetry() -> NxResult<()> {
    get_telemetry().flush_telemetry().await
}

// Crash handler setup
pub fn setup_crash_handler() {
    std::panic::set_hook(Box::new(|panic_info| {
        let operation = "unknown";
        let error_message = panic_info.to_string();
        let stack_trace = Some(format!("{:?}", std::backtrace::Backtrace::capture()));

        // Record crash asynchronously
        tokio::spawn(async move {
            get_telemetry().record_crash(operation, &error_message, stack_trace).await;
            let _ = flush_telemetry().await;
        });
    }));
}
