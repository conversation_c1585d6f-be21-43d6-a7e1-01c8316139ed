{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15625788057656754760, "path": 16215392639322834235, "deps": [[784494742817713399, "tower_service", false, 5636654777102118069], [1906322745568073236, "pin_project_lite", false, 10947738494670116878], [4121350475192885151, "iri_string", false, 16840092318398476007], [5695049318159433696, "tower", false, 3980642350994473926], [7712452662827335977, "tower_layer", false, 14076127404599175995], [7896293946984509699, "bitflags", false, 8036387421423328305], [9010263965687315507, "http", false, 5926620808744162099], [10629569228670356391, "futures_util", false, 15824389852012147554], [14084095096285906100, "http_body", false, 2496751000400218397], [16066129441945555748, "bytes", false, 6765633902541680566]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-http-e78fa6706dbb2cdd\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}