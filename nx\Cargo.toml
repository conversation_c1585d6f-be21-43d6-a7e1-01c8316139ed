[package]
name = "nx"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream", "rustls-tls", "http2", "gzip", "brotli"], default-features = false }
flate2 = "1.0"
tar = "0.4"
clap = { version = "4.0", features = ["derive"] }
indicatif = { version = "0.17", features = ["improved_unicode"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
futures-util = "0.3"
tokio-util = { version = "0.7", features = ["io"] }
dashmap = "5.5"
lru = "0.12"
bytes = "1.5"
async-compression = { version = "0.4", features = ["tokio", "gzip"] }
tower = { version = "0.4", features = ["timeout", "retry", "load-shed"] }
tower-http = { version = "0.4", features = ["compression-gzip"] }
hyper = { version = "0.14", features = ["http2", "client"] }
once_cell = "1.19"
thiserror = "1.0"
memmap2 = "0.9"
rayon = "1.8"
crossbeam-channel = "0.5"
uuid = { version = "1.6", features = ["v4"] }
num_cpus = "1.16"
semver = "1.0"
toml = "0.8"
rustc_version_runtime = "0.3"
chrono = { version = "0.4", features = ["serde"] }

[dev-dependencies]
criterion = "0.5"
tempfile = "3.8"

[[bench]]
name = "performance_benchmark"
harness = false

# Production optimization profiles
[profile.release]
opt-level = 3              # Maximum optimization
lto = "fat"               # Link-time optimization (fat LTO for maximum performance)
codegen-units = 1         # Single codegen unit for better optimization
panic = "abort"           # Abort on panic for smaller binary
strip = true              # Strip debug symbols
overflow-checks = false   # Disable overflow checks in release
debug = false             # No debug info
rpath = false             # No rpath for smaller binary

# Profile-guided optimization profile
[profile.pgo]
inherits = "release"
opt-level = 3
lto = "fat"
codegen-units = 1
panic = "abort"
strip = true

# Optimized development profile
[profile.dev]
opt-level = 1             # Some optimization for faster dev builds
debug = true              # Keep debug info for development
overflow-checks = true    # Keep overflow checks in dev

# Fast compilation profile for CI
[profile.ci]
inherits = "release"
opt-level = 2             # Slightly less optimization for faster CI builds
lto = "thin"              # Thin LTO for faster compilation
codegen-units = 4         # More codegen units for parallel compilation
