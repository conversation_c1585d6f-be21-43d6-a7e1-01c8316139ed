[package]
name = "nx"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream", "rustls-tls", "http2", "gzip", "brotli"], default-features = false }
flate2 = "1.0"
tar = "0.4"
clap = { version = "4.0", features = ["derive"] }
indicatif = { version = "0.17", features = ["improved_unicode"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
futures-util = "0.3"
tokio-util = { version = "0.7", features = ["io"] }
dashmap = "5.5"
lru = "0.12"
bytes = "1.5"
async-compression = { version = "0.4", features = ["tokio", "gzip"] }
tower = { version = "0.4", features = ["timeout", "retry", "load-shed"] }
tower-http = { version = "0.4", features = ["compression-gzip"] }
hyper = { version = "0.14", features = ["http2", "client"] }
once_cell = "1.19"
thiserror = "1.0"
memmap2 = "0.9"
rayon = "1.8"
crossbeam-channel = "0.5"
uuid = { version = "1.6", features = ["v4"] }
num_cpus = "1.16"
semver = "1.0"
toml = "0.8"

[dev-dependencies]
criterion = "0.5"
tempfile = "3.8"

[[bench]]
name = "performance_benchmark"
harness = false
