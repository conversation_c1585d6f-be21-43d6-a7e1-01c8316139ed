// Module declarations
mod cli;
mod installer;
mod registry;
mod progress;
mod utils;
mod package_json;
mod network;
mod cache;
mod memory;
mod resolver;
mod errors;
mod config;
mod audit;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize configuration and validate
    let config = config::get_config();
    if let Err(e) = config.validate() {
        eprintln!("❌ Configuration error: {}", e.user_message());
        if let Some(action) = e.suggested_action() {
            eprintln!("💡 Suggestion: {}", action);
        }
        std::process::exit(1);
    }

    // Initialize logging based on configuration
    init_logging(config);

    // Display startup banner in verbose mode
    if config.ui.verbose {
        display_startup_banner();
    }

    // Start background cleanup tasks
    network::start_cache_cleanup_task();
    cache::start_cache_cleanup_task();

    // Parse command line arguments and execute
    let matches = cli::build_cli().get_matches();

    // Handle the command with proper error handling
    match cli::handle_matches(&matches).await {
        Ok(()) => {
            if config.ui.verbose {
                progress::show_success("Operation completed successfully");
            }
        }
        Err(e) => {
            // Convert to NxError if possible for better error messages
            if let Some(nx_error) = e.downcast_ref::<errors::NxError>() {
                eprintln!("❌ {}", nx_error.user_message());
                if let Some(action) = nx_error.suggested_action() {
                    eprintln!("💡 Suggestion: {}", action);
                }

                // Exit with appropriate code based on error type
                let exit_code = if nx_error.is_user_error() { 1 } else { 2 };
                std::process::exit(exit_code);
            } else {
                eprintln!("❌ {}", e);
                std::process::exit(1);
            }
        }
    }

    Ok(())
}

fn init_logging(config: &config::NxConfig) {
    // Initialize logging based on configuration
    // This would integrate with a proper logging framework in production
    if config.ui.verbose {
        std::env::set_var("RUST_LOG", "debug");
    } else if config.ui.quiet {
        std::env::set_var("RUST_LOG", "error");
    } else {
        std::env::set_var("RUST_LOG", &config.ui.log_level);
    }
}

fn display_startup_banner() {
    println!("🚀 \x1b[1;36mNX Package Manager\x1b[0m v{}", env!("CARGO_PKG_VERSION"));
    println!("⚡ Ultra-fast package management with advanced optimizations");
    println!("🔧 Configuration loaded successfully");
    println!();
}
