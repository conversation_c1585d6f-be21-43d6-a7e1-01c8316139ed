{"version": 3, "file": "BrowserContext.js", "sourceRoot": "", "sources": ["../../../../src/cdp/BrowserContext.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,kDAG2B;AAC3B,gEAAwD;AAIxD,iDAAyC;AAIzC,uCAAuE;AAGvE;;GAEG;AACH,MAAa,iBAAkB,SAAQ,kCAAc;IACnD,WAAW,CAAa;IACxB,QAAQ,CAAa;IACrB,GAAG,CAAU;IAEb,YAAY,UAAsB,EAAE,OAAmB,EAAE,SAAkB;QACzE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC;IACvB,CAAC;IAED,IAAa,EAAE;QACb,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7C,OAAO,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,IAAI,CAAC,OAAO,EAAE;aACX,MAAM,CAAC,MAAM,CAAC,EAAE;YACf,OAAO,CACL,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM;gBACxB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,OAAO;oBACxB,IAAI,CAAC,QAAQ,CAAC,wBAAwB,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CACtD,CAAC;QACJ,CAAC,CAAC;aACD,GAAG,CAAC,MAAM,CAAC,EAAE;YACZ,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;QACvB,CAAC,CAAC,CACL,CAAC;QACF,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAgB,EAAE;YACzC,OAAO,CAAC,CAAC,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,mBAAmB,CAChC,MAAc,EACd,WAAyB;QAEzB,MAAM,mBAAmB,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACvD,MAAM,kBAAkB,GACtB,kDAAqC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,UAAU,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,kBAAkB,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtD,MAAM;YACN,gBAAgB,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;YACvC,WAAW,EAAE,mBAAmB;SACjC,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,wBAAwB;QACrC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtD,gBAAgB,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;SACxC,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,OAAO;;;YACpB,MAAM,MAAM,kCAAG,MAAM,IAAI,CAAC,2BAA2B,EAAE,QAAA,CAAC;YACxD,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;;;;;;KAC3D;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,IAAA,kBAAM,EAAC,IAAI,CAAC,GAAG,EAAE,0CAA0C,CAAC,CAAC;QAC7D,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,MAAM,EAAC,OAAO,EAAC,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAClE,gBAAgB,EAAE,IAAI,CAAC,GAAG;SAC3B,CAAC,CAAC;QACH,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC1B,OAAO;gBACL,GAAG,MAAM;gBACT,YAAY,EAAE,MAAM,CAAC,YAAY;oBAC/B,CAAC,CAAC;wBACE,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,YAAY;wBAC9C,oBAAoB,EAAE,MAAM,CAAC,YAAY,CAAC,oBAAoB;qBAC/D;oBACH,CAAC,CAAC,SAAS;aACd,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,SAAS,CAAC,GAAG,OAAqB;QAC/C,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACvD,gBAAgB,EAAE,IAAI,CAAC,GAAG;YAC1B,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC5B,OAAO;oBACL,GAAG,MAAM;oBACT,YAAY,EAAE,IAAA,sDAA4C,EACxD,MAAM,CAAC,YAAY,CACpB;iBACF,CAAC;YACJ,CAAC,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAC9B,gBAAkC;QAElC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACzD,QAAQ,EAAE,gBAAgB,CAAC,MAAM;YACjC,YAAY,EAAE,gBAAgB,CAAC,YAAY;YAC3C,gBAAgB,EAAE,IAAI,CAAC,GAAG;SAC3B,CAAC,CAAC;IACL,CAAC;CACF;AAxHD,8CAwHC"}