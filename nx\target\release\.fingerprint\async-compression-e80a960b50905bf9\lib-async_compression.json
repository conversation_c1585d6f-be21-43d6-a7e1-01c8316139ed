{"rustc": 1842507548689473721, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 3592778941406178886, "path": 5623080562496243947, "deps": [[1906322745568073236, "pin_project_lite", false, 8023542118904931378], [7620660491849607393, "futures_core", false, 8042466654741058620], [9556762810601084293, "brotli", false, 4672659101669526650], [12393800526703971956, "tokio", false, 4223665201178837825], [15932120279885307830, "memchr", false, 18221823195976032050], [17772299992546037086, "flate2", false, 13009739626666389897]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\async-compression-e80a960b50905bf9\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}