// Advanced profiling and performance optimization system

use crate::config::get_config;
use crate::telemetry::{PerformanceMonitor, get_telemetry};
use std::time::{Duration, Instant};
use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use once_cell::sync::Lazy;

// Global profiler instance
static PROFILER: Lazy<Arc<Profiler>> = Lazy::new(|| {
    Arc::new(Profiler::new())
});

#[derive(Debug, Clone)]
pub struct ProfileData {
    pub function_name: String,
    pub total_time: Duration,
    pub call_count: u64,
    pub avg_time: Duration,
    pub min_time: Duration,
    pub max_time: Duration,
    pub memory_delta: Option<i64>,
}

#[derive(Debug, Clone)]
pub struct HotPath {
    pub path: Vec<String>,
    pub total_time: Duration,
    pub frequency: u64,
    pub optimization_potential: f64,
}

pub struct Profiler {
    profiles: Arc<RwLock<HashMap<String, Vec<Duration>>>>,
    call_stack: Arc<RwLock<Vec<String>>>,
    enabled: bool,
}

impl Profiler {
    pub fn new() -> Self {
        let config = get_config();
        Self {
            profiles: Arc::new(RwLock::new(HashMap::new())),
            call_stack: Arc::new(RwLock::new(Vec::new())),
            enabled: config.ui.verbose, // Enable profiling in verbose mode
        }
    }

    pub async fn profile<F, R>(&self, name: &str, f: F) -> R
    where
        F: std::future::Future<Output = R>,
    {
        if !self.enabled {
            return f.await;
        }

        let start = Instant::now();
        
        // Add to call stack
        {
            let mut stack = self.call_stack.write().await;
            stack.push(name.to_string());
        }

        let result = f.await;
        
        let duration = start.elapsed();

        // Remove from call stack
        {
            let mut stack = self.call_stack.write().await;
            stack.pop();
        }

        // Record timing
        {
            let mut profiles = self.profiles.write().await;
            profiles.entry(name.to_string()).or_default().push(duration);
        }

        result
    }

    pub async fn get_profile_data(&self) -> Vec<ProfileData> {
        let profiles = self.profiles.read().await;
        let mut data = Vec::new();

        for (name, times) in profiles.iter() {
            if times.is_empty() {
                continue;
            }

            let total_time: Duration = times.iter().sum();
            let call_count = times.len() as u64;
            let avg_time = total_time / call_count as u32;
            let min_time = *times.iter().min().unwrap();
            let max_time = *times.iter().max().unwrap();

            data.push(ProfileData {
                function_name: name.clone(),
                total_time,
                call_count,
                avg_time,
                min_time,
                max_time,
                memory_delta: None, // Could be enhanced with memory tracking
            });
        }

        // Sort by total time descending
        data.sort_by(|a, b| b.total_time.cmp(&a.total_time));
        data
    }

    pub async fn identify_hot_paths(&self) -> Vec<HotPath> {
        let profiles = self.profiles.read().await;
        let mut hot_paths = Vec::new();

        // Identify functions that take the most time
        for (name, times) in profiles.iter() {
            if times.is_empty() {
                continue;
            }

            let total_time: Duration = times.iter().sum();
            let frequency = times.len() as u64;
            
            // Calculate optimization potential based on time and frequency
            let optimization_potential = (total_time.as_millis() as f64) * (frequency as f64).log2();

            if optimization_potential > 100.0 { // Threshold for significant optimization potential
                hot_paths.push(HotPath {
                    path: vec![name.clone()],
                    total_time,
                    frequency,
                    optimization_potential,
                });
            }
        }

        // Sort by optimization potential
        hot_paths.sort_by(|a, b| b.optimization_potential.partial_cmp(&a.optimization_potential).unwrap());
        hot_paths
    }

    pub async fn generate_optimization_report(&self) -> OptimizationReport {
        let profile_data = self.get_profile_data().await;
        let hot_paths = self.identify_hot_paths().await;

        let total_execution_time: Duration = profile_data.iter().map(|p| p.total_time).sum();
        let total_calls: u64 = profile_data.iter().map(|p| p.call_count).sum();

        OptimizationReport {
            total_execution_time,
            total_calls,
            top_functions: profile_data.into_iter().take(10).collect(),
            hot_paths: hot_paths.into_iter().take(5).collect(),
            recommendations: generate_optimization_recommendations(&profile_data, &hot_paths),
        }
    }

    pub async fn clear_profiles(&self) {
        let mut profiles = self.profiles.write().await;
        profiles.clear();
    }
}

#[derive(Debug)]
pub struct OptimizationReport {
    pub total_execution_time: Duration,
    pub total_calls: u64,
    pub top_functions: Vec<ProfileData>,
    pub hot_paths: Vec<HotPath>,
    pub recommendations: Vec<OptimizationRecommendation>,
}

#[derive(Debug, Clone)]
pub struct OptimizationRecommendation {
    pub category: String,
    pub description: String,
    pub potential_improvement: String,
    pub implementation_effort: String,
}

// Adaptive performance scaling based on system resources
pub struct AdaptiveScaler {
    cpu_cores: usize,
    available_memory: u64,
    network_latency: Duration,
    current_load: f64,
}

impl AdaptiveScaler {
    pub fn new() -> Self {
        Self {
            cpu_cores: num_cpus::get(),
            available_memory: get_available_memory(),
            network_latency: Duration::from_millis(50), // Default
            current_load: 0.0,
        }
    }

    pub async fn get_optimal_concurrency(&self, operation_type: &str) -> usize {
        match operation_type {
            "network_requests" => {
                // Scale based on network latency and available memory
                let base_concurrency = self.cpu_cores * 4;
                let latency_factor = if self.network_latency.as_millis() < 100 {
                    2.0
                } else if self.network_latency.as_millis() < 500 {
                    1.5
                } else {
                    1.0
                };
                
                let memory_factor = if self.available_memory > 4096 * 1024 * 1024 { // 4GB
                    1.5
                } else {
                    1.0
                };

                ((base_concurrency as f64) * latency_factor * memory_factor) as usize
            }
            "file_operations" => {
                // Scale based on CPU cores and current load
                let base_concurrency = self.cpu_cores;
                let load_factor = if self.current_load < 0.5 {
                    2.0
                } else if self.current_load < 0.8 {
                    1.5
                } else {
                    1.0
                };

                ((base_concurrency as f64) * load_factor) as usize
            }
            "cpu_intensive" => {
                // For CPU-intensive tasks, use CPU cores with load consideration
                let load_factor = if self.current_load < 0.3 {
                    1.0
                } else if self.current_load < 0.7 {
                    0.8
                } else {
                    0.5
                };

                ((self.cpu_cores as f64) * load_factor).max(1.0) as usize
            }
            _ => self.cpu_cores
        }
    }

    pub async fn update_system_metrics(&mut self) {
        // Update available memory
        self.available_memory = get_available_memory();
        
        // Update current load (simplified)
        self.current_load = get_current_load();
        
        // Update network latency (could ping registry)
        self.network_latency = measure_network_latency().await;
    }

    pub fn get_memory_threshold(&self) -> usize {
        // Return memory threshold for switching to memory-mapped operations
        if self.available_memory > 8192 * 1024 * 1024 { // 8GB
            10 * 1024 * 1024 // 10MB threshold
        } else if self.available_memory > 4096 * 1024 * 1024 { // 4GB
            5 * 1024 * 1024 // 5MB threshold
        } else {
            2 * 1024 * 1024 // 2MB threshold
        }
    }
}

// Performance monitoring and metrics collection
pub struct PerformanceCollector {
    metrics: Arc<RwLock<Vec<PerformanceMetric>>>,
}

#[derive(Debug, Clone)]
pub struct PerformanceMetric {
    pub timestamp: Instant,
    pub operation: String,
    pub duration: Duration,
    pub memory_usage: u64,
    pub cpu_usage: f64,
    pub network_bytes: u64,
    pub cache_hit_rate: f64,
}

impl PerformanceCollector {
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(RwLock::new(Vec::new())),
        }
    }

    pub async fn record_metric(&self, metric: PerformanceMetric) {
        let mut metrics = self.metrics.write().await;
        metrics.push(metric);

        // Keep only last 1000 metrics
        if metrics.len() > 1000 {
            metrics.drain(0..metrics.len() - 1000);
        }
    }

    pub async fn get_performance_trends(&self) -> PerformanceTrends {
        let metrics = self.metrics.read().await;
        
        if metrics.is_empty() {
            return PerformanceTrends::default();
        }

        let avg_duration = metrics.iter().map(|m| m.duration.as_millis()).sum::<u128>() / metrics.len() as u128;
        let avg_memory = metrics.iter().map(|m| m.memory_usage).sum::<u64>() / metrics.len() as u64;
        let avg_cpu = metrics.iter().map(|m| m.cpu_usage).sum::<f64>() / metrics.len() as f64;
        let avg_cache_hit_rate = metrics.iter().map(|m| m.cache_hit_rate).sum::<f64>() / metrics.len() as f64;

        PerformanceTrends {
            avg_duration_ms: avg_duration as u64,
            avg_memory_usage: avg_memory,
            avg_cpu_usage: avg_cpu,
            avg_cache_hit_rate,
            total_operations: metrics.len(),
        }
    }
}

#[derive(Debug, Default)]
pub struct PerformanceTrends {
    pub avg_duration_ms: u64,
    pub avg_memory_usage: u64,
    pub avg_cpu_usage: f64,
    pub avg_cache_hit_rate: f64,
    pub total_operations: usize,
}

// Utility functions
fn generate_optimization_recommendations(
    profile_data: &[ProfileData],
    hot_paths: &[HotPath],
) -> Vec<OptimizationRecommendation> {
    let mut recommendations = Vec::new();

    // Analyze top time-consuming functions
    if let Some(top_function) = profile_data.first() {
        if top_function.total_time.as_millis() > 1000 {
            recommendations.push(OptimizationRecommendation {
                category: "Hot Function".to_string(),
                description: format!("Function '{}' consumes {}ms total time", 
                    top_function.function_name, top_function.total_time.as_millis()),
                potential_improvement: "20-50% performance gain".to_string(),
                implementation_effort: "Medium".to_string(),
            });
        }
    }

    // Analyze frequently called functions
    for profile in profile_data.iter().take(5) {
        if profile.call_count > 100 && profile.avg_time.as_millis() > 10 {
            recommendations.push(OptimizationRecommendation {
                category: "Frequent Function".to_string(),
                description: format!("Function '{}' called {} times with {}ms average", 
                    profile.function_name, profile.call_count, profile.avg_time.as_millis()),
                potential_improvement: "10-30% performance gain".to_string(),
                implementation_effort: "Low".to_string(),
            });
        }
    }

    recommendations
}

fn get_available_memory() -> u64 {
    // Simplified - would use platform-specific APIs in production
    8192 * 1024 * 1024 // 8GB default
}

fn get_current_load() -> f64 {
    // Simplified - would use platform-specific APIs in production
    0.3 // 30% default load
}

async fn measure_network_latency() -> Duration {
    // Simplified - would ping actual registry in production
    Duration::from_millis(50)
}

// Global access functions
pub fn get_profiler() -> &'static Profiler {
    &PROFILER
}

pub async fn profile_async<F, R>(name: &str, f: F) -> R
where
    F: std::future::Future<Output = R>,
{
    get_profiler().profile(name, f).await
}

// Macro for easy profiling
#[macro_export]
macro_rules! profile {
    ($name:expr, $code:block) => {
        $crate::profiler::profile_async($name, async move $code).await
    };
}

pub use profile;
