# Production build script for nx package manager (Windows)

param(
    [string]$Version = ""
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Configuration
if (-not $Version) {
    $Version = (cargo metadata --no-deps --format-version 1 | ConvertFrom-Json).packages[0].version
}

$BuildDir = "target\release-builds"
$Targets = @(
    "x86_64-pc-windows-msvc",
    "x86_64-pc-windows-gnu",
    "i686-pc-windows-msvc",
    "aarch64-pc-windows-msvc"
)

Write-Host "🚀 Building nx package manager v$Version for production" -ForegroundColor $Blue
Write-Host "================================================" -ForegroundColor $Blue

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor $Yellow
if (Test-Path $BuildDir) {
    Remove-Item -Recurse -Force $BuildDir
}
New-Item -ItemType Directory -Path $BuildDir -Force | Out-Null

# Install required targets
Write-Host "🔧 Installing cross-compilation targets..." -ForegroundColor $Yellow
foreach ($target in $Targets) {
    Write-Host "  Installing $target..." -ForegroundColor $Yellow
    rustup target add $target
    if ($LASTEXITCODE -ne 0) {
        Write-Host "  Warning: Could not install $target" -ForegroundColor $Yellow
    }
}

# Function to build for a specific target
function Build-Target {
    param([string]$Target)
    
    $OutputDir = "$BuildDir\$Target"
    $BinaryName = "nx.exe"
    
    Write-Host "🔨 Building for $Target..." -ForegroundColor $Blue
    
    # Build with maximum optimization
    $env:RUSTFLAGS = "-C target-cpu=native -C link-arg=/SUBSYSTEM:CONSOLE"
    cargo build --release --target $Target --bin nx
    
    if ($LASTEXITCODE -eq 0) {
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
        Copy-Item "target\$Target\release\$BinaryName" $OutputDir\
        
        # Get binary size
        $Size = (Get-Item "$OutputDir\$BinaryName").Length
        $SizeKB = [math]::Round($Size / 1KB, 2)
        Write-Host "✅ Built $Target ($SizeKB KB)" -ForegroundColor $Green
        
        # Create archive
        Push-Location $OutputDir
        Compress-Archive -Path $BinaryName -DestinationPath "..\nx-$Version-$Target.zip" -Force
        Pop-Location
        
        Write-Host "📦 Created archive for $Target" -ForegroundColor $Green
        return $true
    } else {
        Write-Host "❌ Failed to build $Target" -ForegroundColor $Red
        return $false
    }
}

# Build for all targets
Write-Host "🏗️  Building for all targets..." -ForegroundColor $Yellow
$FailedTargets = @()

foreach ($target in $Targets) {
    if (-not (Build-Target $target)) {
        $FailedTargets += $target
    }
}

# Generate checksums
Write-Host "🔐 Generating checksums..." -ForegroundColor $Yellow
Push-Location $BuildDir
Get-ChildItem -Filter "*.zip" | ForEach-Object {
    $Hash = Get-FileHash $_.Name -Algorithm SHA256
    "$($Hash.Hash)  $($_.Name)" | Out-File -Append "nx-$Version-checksums.txt" -Encoding UTF8
}
Pop-Location

# Build summary
Write-Host "================================================" -ForegroundColor $Blue
Write-Host "🎉 Build Summary" -ForegroundColor $Green
Write-Host "Version: $Version" -ForegroundColor $Green
Write-Host "Successful builds: $(($Targets.Count - $FailedTargets.Count))/$($Targets.Count)" -ForegroundColor $Green

if ($FailedTargets.Count -gt 0) {
    Write-Host "Failed targets: $($FailedTargets -join ', ')" -ForegroundColor $Red
}

Write-Host "Build artifacts available in: $BuildDir" -ForegroundColor $Green
Write-Host "Checksums: $BuildDir\nx-$Version-checksums.txt" -ForegroundColor $Green

# Performance test on native build
if (Test-Path "target\release\nx.exe") {
    Write-Host "🚀 Running quick performance test..." -ForegroundColor $Yellow
    Measure-Command { .\target\release\nx.exe --version | Out-Null }
    Write-Host "✅ Performance test completed" -ForegroundColor $Green
}

Write-Host "🚀 Production build completed successfully!" -ForegroundColor $Blue
