{"rustc": 1842507548689473721, "features": "[\"default\", \"improved_unicode\", \"unicode-segmentation\", \"unicode-width\"]", "declared_features": "[\"default\", \"futures\", \"improved_unicode\", \"in_memory\", \"rayon\", \"tokio\", \"unicode-segmentation\", \"unicode-width\", \"vt100\"]", "target": 10301169156738538477, "profile": 3592778941406178886, "path": 10564265116035168945, "deps": [[1232198224951696867, "unicode_segmentation", false, 11992116217362525905], [3958489542916937055, "portable_atomic", false, 12739288366565505211], [11485413305714879807, "console", false, 8663900887631211782], [13774335185398496026, "unicode_width", false, 5843603903508416151], [14188466555567159420, "number_prefix", false, 4628411108449966998]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\indicatif-0aa82f0b99db999c\\dep-lib-indicatif", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}