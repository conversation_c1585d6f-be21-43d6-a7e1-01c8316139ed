// Comprehensive error handling for production-ready package manager

use thiserror::<PERSON>rro<PERSON>;
use std::fmt;

#[derive(Erro<PERSON>, Debug)]
pub enum NxError {
    #[error("Network error: {message}")]
    Network { message: String, source: Option<Box<dyn std::error::Error + Send + Sync>> },
    
    #[error("Package '{package}' not found")]
    PackageNotFound { package: String },
    
    #[error("Version conflict for package '{package}': {details}")]
    VersionConflict { package: String, details: String },
    
    #[error("Circular dependency detected: {cycle}")]
    CircularDependency { cycle: String },
    
    #[error("IO error: {message}")]
    Io { message: String, path: Option<std::path::PathBuf> },
    
    #[error("Parse error: {message}")]
    Parse { message: String, context: Option<String> },
    
    #[error("Configuration error: {message}")]
    Config { message: String },
    
    #[error("Permission denied: {message}")]
    Permission { message: String, path: Option<std::path::PathBuf> },
    
    #[error("Cache error: {message}")]
    Cache { message: String },
    
    #[error("Script execution failed: {script}")]
    Script { script: String, exit_code: Option<i32> },
    
    #[error("Workspace error: {message}")]
    Workspace { message: String },
    
    #[error("Validation error: {message}")]
    Validation { message: String, field: Option<String> },
    
    #[error("Timeout error: {operation} timed out after {duration_secs}s")]
    Timeout { operation: String, duration_secs: u64 },
    
    #[error("Integrity check failed for package '{package}'")]
    Integrity { package: String, expected: Option<String>, actual: Option<String> },
    
    #[error("Insufficient disk space: {required} bytes required, {available} bytes available")]
    DiskSpace { required: u64, available: u64 },
    
    #[error("Rate limit exceeded: {message}")]
    RateLimit { message: String, retry_after: Option<u64> },
    
    #[error("Authentication error: {message}")]
    Auth { message: String },
    
    #[error("Internal error: {message}")]
    Internal { message: String },
}

impl NxError {
    // Convenience constructors
    pub fn network<S: Into<String>>(message: S) -> Self {
        Self::Network { message: message.into(), source: None }
    }
    
    pub fn network_with_source<S: Into<String>>(message: S, source: Box<dyn std::error::Error + Send + Sync>) -> Self {
        Self::Network { message: message.into(), source: Some(source) }
    }
    
    pub fn package_not_found<S: Into<String>>(package: S) -> Self {
        Self::PackageNotFound { package: package.into() }
    }
    
    pub fn version_conflict<S: Into<String>, T: Into<String>>(package: S, details: T) -> Self {
        Self::VersionConflict { package: package.into(), details: details.into() }
    }
    
    pub fn circular_dependency<S: Into<String>>(cycle: S) -> Self {
        Self::CircularDependency { cycle: cycle.into() }
    }
    
    pub fn io<S: Into<String>>(message: S) -> Self {
        Self::Io { message: message.into(), path: None }
    }
    
    pub fn io_with_path<S: Into<String>, P: Into<std::path::PathBuf>>(message: S, path: P) -> Self {
        Self::Io { message: message.into(), path: Some(path.into()) }
    }
    
    pub fn parse<S: Into<String>>(message: S) -> Self {
        Self::Parse { message: message.into(), context: None }
    }
    
    pub fn parse_with_context<S: Into<String>, T: Into<String>>(message: S, context: T) -> Self {
        Self::Parse { message: message.into(), context: Some(context.into()) }
    }
    
    pub fn config<S: Into<String>>(message: S) -> Self {
        Self::Config { message: message.into() }
    }
    
    pub fn permission<S: Into<String>>(message: S) -> Self {
        Self::Permission { message: message.into(), path: None }
    }
    
    pub fn permission_with_path<S: Into<String>, P: Into<std::path::PathBuf>>(message: S, path: P) -> Self {
        Self::Permission { message: message.into(), path: Some(path.into()) }
    }
    
    pub fn cache<S: Into<String>>(message: S) -> Self {
        Self::Cache { message: message.into() }
    }
    
    pub fn script<S: Into<String>>(script: S, exit_code: Option<i32>) -> Self {
        Self::Script { script: script.into(), exit_code }
    }
    
    pub fn workspace<S: Into<String>>(message: S) -> Self {
        Self::Workspace { message: message.into() }
    }
    
    pub fn validation<S: Into<String>>(message: S) -> Self {
        Self::Validation { message: message.into(), field: None }
    }
    
    pub fn validation_with_field<S: Into<String>, T: Into<String>>(message: S, field: T) -> Self {
        Self::Validation { message: message.into(), field: Some(field.into()) }
    }
    
    pub fn timeout<S: Into<String>>(operation: S, duration_secs: u64) -> Self {
        Self::Timeout { operation: operation.into(), duration_secs }
    }
    
    pub fn integrity<S: Into<String>>(package: S) -> Self {
        Self::Integrity { package: package.into(), expected: None, actual: None }
    }
    
    pub fn integrity_with_checksums<S: Into<String>, T: Into<String>, U: Into<String>>(
        package: S, expected: T, actual: U
    ) -> Self {
        Self::Integrity { 
            package: package.into(), 
            expected: Some(expected.into()), 
            actual: Some(actual.into()) 
        }
    }
    
    pub fn disk_space(required: u64, available: u64) -> Self {
        Self::DiskSpace { required, available }
    }
    
    pub fn rate_limit<S: Into<String>>(message: S) -> Self {
        Self::RateLimit { message: message.into(), retry_after: None }
    }
    
    pub fn rate_limit_with_retry<S: Into<String>>(message: S, retry_after: u64) -> Self {
        Self::RateLimit { message: message.into(), retry_after: Some(retry_after) }
    }
    
    pub fn auth<S: Into<String>>(message: S) -> Self {
        Self::Auth { message: message.into() }
    }
    
    pub fn internal<S: Into<String>>(message: S) -> Self {
        Self::Internal { message: message.into() }
    }
    
    // Error categorization
    pub fn is_recoverable(&self) -> bool {
        match self {
            Self::Network { .. } => true,
            Self::Timeout { .. } => true,
            Self::RateLimit { .. } => true,
            Self::Cache { .. } => true,
            _ => false,
        }
    }
    
    pub fn is_user_error(&self) -> bool {
        match self {
            Self::PackageNotFound { .. } => true,
            Self::Config { .. } => true,
            Self::Validation { .. } => true,
            Self::Auth { .. } => true,
            _ => false,
        }
    }
    
    pub fn is_system_error(&self) -> bool {
        match self {
            Self::Io { .. } => true,
            Self::Permission { .. } => true,
            Self::DiskSpace { .. } => true,
            _ => false,
        }
    }
    
    // User-friendly error messages
    pub fn user_message(&self) -> String {
        match self {
            Self::Network { message, .. } => {
                format!("Network connection failed: {}. Please check your internet connection and try again.", message)
            }
            Self::PackageNotFound { package } => {
                format!("Package '{}' was not found. Please check the package name and try again.", package)
            }
            Self::VersionConflict { package, details } => {
                format!("Version conflict for '{}': {}. Try updating your dependencies.", package, details)
            }
            Self::CircularDependency { cycle } => {
                format!("Circular dependency detected: {}. Please review your package dependencies.", cycle)
            }
            Self::Io { message, path } => {
                match path {
                    Some(p) => format!("File system error at '{}': {}", p.display(), message),
                    None => format!("File system error: {}", message),
                }
            }
            Self::Permission { message, path } => {
                match path {
                    Some(p) => format!("Permission denied for '{}': {}. Try running with elevated privileges.", p.display(), message),
                    None => format!("Permission denied: {}. Try running with elevated privileges.", message),
                }
            }
            Self::DiskSpace { required, available } => {
                format!("Insufficient disk space. Required: {} MB, Available: {} MB", 
                    required / 1024 / 1024, available / 1024 / 1024)
            }
            Self::RateLimit { message, retry_after } => {
                match retry_after {
                    Some(seconds) => format!("Rate limit exceeded: {}. Please wait {} seconds before retrying.", message, seconds),
                    None => format!("Rate limit exceeded: {}. Please wait before retrying.", message),
                }
            }
            Self::Timeout { operation, duration_secs } => {
                format!("Operation '{}' timed out after {} seconds. Please try again.", operation, duration_secs)
            }
            _ => self.to_string(),
        }
    }
    
    // Suggested actions for recovery
    pub fn suggested_action(&self) -> Option<String> {
        match self {
            Self::Network { .. } => Some("Check your internet connection and proxy settings".to_string()),
            Self::PackageNotFound { .. } => Some("Verify the package name or search for similar packages".to_string()),
            Self::Permission { .. } => Some("Run the command with administrator/sudo privileges".to_string()),
            Self::DiskSpace { .. } => Some("Free up disk space and try again".to_string()),
            Self::RateLimit { .. } => Some("Wait a few minutes before retrying".to_string()),
            Self::Config { .. } => Some("Check your configuration file for errors".to_string()),
            _ => None,
        }
    }
}

// Result type alias for convenience
pub type NxResult<T> = Result<T, NxError>;

// Error context trait for adding context to errors
pub trait ErrorContext<T> {
    fn with_context<F>(self, f: F) -> NxResult<T>
    where
        F: FnOnce() -> String;
    
    fn with_package_context<S: Into<String>>(self, package: S) -> NxResult<T>;
}

impl<T, E> ErrorContext<T> for Result<T, E>
where
    E: std::error::Error + Send + Sync + 'static,
{
    fn with_context<F>(self, f: F) -> NxResult<T>
    where
        F: FnOnce() -> String,
    {
        self.map_err(|e| NxError::internal(format!("{}: {}", f(), e)))
    }
    
    fn with_package_context<S: Into<String>>(self, package: S) -> NxResult<T> {
        let package = package.into();
        self.map_err(|e| NxError::internal(format!("Error processing package '{}': {}", package, e)))
    }
}

// Conversion implementations
impl From<std::io::Error> for NxError {
    fn from(error: std::io::Error) -> Self {
        match error.kind() {
            std::io::ErrorKind::PermissionDenied => Self::permission(error.to_string()),
            std::io::ErrorKind::NotFound => Self::io(format!("File or directory not found: {}", error)),
            std::io::ErrorKind::TimedOut => Self::timeout("IO operation", 30),
            _ => Self::io(error.to_string()),
        }
    }
}

impl From<serde_json::Error> for NxError {
    fn from(error: serde_json::Error) -> Self {
        Self::parse(format!("JSON parsing failed: {}", error))
    }
}

impl From<reqwest::Error> for NxError {
    fn from(error: reqwest::Error) -> Self {
        if error.is_timeout() {
            Self::timeout("Network request", 30)
        } else if error.is_connect() {
            Self::network("Failed to connect to server")
        } else {
            Self::network(error.to_string())
        }
    }
}
