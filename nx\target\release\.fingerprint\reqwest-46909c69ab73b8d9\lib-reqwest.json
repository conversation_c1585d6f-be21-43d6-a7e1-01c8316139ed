{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"brotli\", \"gzip\", \"h2\", \"http2\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 4715155986431905501, "path": 11523913256638171667, "deps": [[40386456601120721, "percent_encoding", false, 707603578821916170], [41016358116313498, "hyper_util", false, 12936134084200506558], [784494742817713399, "tower_service", false, 4788937879189487147], [1288403060204016458, "tokio_util", false, 5602823022161294318], [1788832197870803419, "hyper_rustls", false, 566642903956206067], [1906322745568073236, "pin_project_lite", false, 8023542118904931378], [2054153378684941554, "tower_http", false, 3878489397758887176], [2517136641825875337, "sync_wrapper", false, 17799758465805697695], [2883436298747778685, "rustls_pki_types", false, 6249722070552777526], [3150220818285335163, "url", false, 5405985481695640301], [5695049318159433696, "tower", false, 4543005089213415137], [5986029879202738730, "log", false, 10872228379112573331], [7620660491849607393, "futures_core", false, 8042466654741058620], [8153991275959898788, "webpki_roots", false, 8149879891668776693], [8569119365930580996, "serde_json", false, 2130465006604748677], [9010263965687315507, "http", false, 1892032584777174270], [9689903380558560274, "serde", false, 13641539003243914431], [10629569228670356391, "futures_util", false, 17717833475852592514], [11895591994124935963, "tokio_rustls", false, 18011413126716263812], [11957360342995674422, "hyper", false, 12178671363772744532], [12393800526703971956, "tokio", false, 4223665201178837825], [13077212702700853852, "base64", false, 15407962630934836165], [14084095096285906100, "http_body", false, 17240980880396472347], [14359893265615549706, "h2", false, 1026034119023946496], [14619257664405537057, "rustls", false, 12358600061817176247], [14721851354164625169, "async_compression", false, 11025336333706271468], [16066129441945555748, "bytes", false, 6440951242105093202], [16542808166767769916, "serde_urlencoded", false, 16890468311175848332], [16900715236047033623, "http_body_util", false, 14378812854043118469]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-46909c69ab73b8d9\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}