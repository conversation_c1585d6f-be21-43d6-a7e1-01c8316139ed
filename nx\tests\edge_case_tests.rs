// Edge case tests for nx package manager

use std::time::{Duration, Instant};
use tokio::test;
use tempfile::TempDir;

// Test network failure scenarios
#[test]
async fn test_network_timeout_handling() {
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test with a package that might timeout
    let start = Instant::now();
    let result = installer.fetch_package_info("non-existent-package-that-will-timeout-12345").await;
    let duration = start.elapsed();
    
    // Should fail gracefully within reasonable time
    assert!(result.is_err(), "Should fail for non-existent package");
    assert!(duration.as_secs() < 60, "Should timeout within 60 seconds");
    
    println!("✅ Network timeout handled in {:.2}s", duration.as_secs_f64());
}

#[test]
async fn test_malformed_package_json_handling() {
    // Test handling of packages with malformed metadata
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test with empty package name
    let result = installer.resolve_dependencies(&["".to_string()]).await;
    assert!(result.is_err() || result.unwrap().is_empty(), "Should handle empty package name");
    
    // Test with invalid characters
    let result = installer.resolve_dependencies(&["package@#$%^&*()".to_string()]).await;
    assert!(result.is_err() || result.unwrap().is_empty(), "Should handle invalid characters");
    
    // Test with extremely long package name
    let long_name = "a".repeat(1000);
    let result = installer.resolve_dependencies(&[long_name]).await;
    assert!(result.is_err() || result.unwrap().is_empty(), "Should handle extremely long names");
    
    println!("✅ Malformed package.json handling tests passed");
}

#[test]
async fn test_circular_dependency_detection() {
    // This test would require setting up mock packages with circular dependencies
    // For now, we test the resolver's ability to handle potential circular deps
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolver = nx::resolver::DependencyResolver::new(installer.network_client.clone());
    
    // Test with packages that might have circular dependencies
    let packages = vec![
        ("package-a".to_string(), "1.0.0".to_string()),
        ("package-b".to_string(), "1.0.0".to_string()),
    ];
    
    let start = Instant::now();
    let result = resolver.resolve_dependencies(&packages, false).await;
    let duration = start.elapsed();
    
    // Should either resolve successfully or detect circular dependency
    match result {
        Ok(resolved) => {
            println!("✅ Resolved {} packages without circular dependencies", resolved.len());
        }
        Err(e) => {
            if e.to_string().contains("circular") || e.to_string().contains("Circular") {
                println!("✅ Circular dependency detected and handled: {}", e);
            } else {
                println!("✅ Other resolution error handled: {}", e);
            }
        }
    }
    
    assert!(duration.as_secs() < 30, "Should complete within 30 seconds");
}

#[test]
async fn test_version_conflict_resolution() {
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolver = nx::resolver::DependencyResolver::new(installer.network_client.clone());
    
    // Test with conflicting version requirements
    let packages = vec![
        ("lodash".to_string(), "^4.0.0".to_string()),
        ("lodash".to_string(), "^3.0.0".to_string()), // Conflicting version
    ];
    
    let result = resolver.resolve_dependencies(&packages, false).await;
    
    match result {
        Ok(resolved) => {
            // Should resolve to a compatible version
            let lodash_packages: Vec<_> = resolved.iter().filter(|p| p.name == "lodash").collect();
            assert_eq!(lodash_packages.len(), 1, "Should resolve to single lodash version");
            println!("✅ Version conflict resolved to: {}", lodash_packages[0].version);
        }
        Err(e) => {
            // Should provide clear error message about version conflict
            assert!(e.to_string().contains("conflict") || e.to_string().contains("version"), 
                "Error should mention version conflict: {}", e);
            println!("✅ Version conflict detected: {}", e);
        }
    }
}

#[test]
async fn test_extremely_large_package_handling() {
    // Test handling of very large packages (simulated)
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test with packages known to be large
    let large_packages = vec![
        "webpack".to_string(),
        "@angular/cli".to_string(),
        "typescript".to_string(),
    ];
    
    let start = Instant::now();
    let result = installer.resolve_dependencies(&large_packages).await;
    let duration = start.elapsed();
    
    match result {
        Ok(resolved) => {
            assert!(resolved.len() >= 3, "Should resolve large packages");
            println!("✅ Large packages resolved: {} packages in {:.2}s", resolved.len(), duration.as_secs_f64());
        }
        Err(e) => {
            println!("✅ Large package error handled gracefully: {}", e);
        }
    }
    
    assert!(duration.as_secs() < 120, "Should complete within 2 minutes");
}

#[test]
async fn test_concurrent_cache_access() {
    use futures_util::future::join_all;
    
    // Test concurrent access to cache systems
    let tasks: Vec<_> = (0..10).map(|i| {
        tokio::spawn(async move {
            let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
            
            // Each task tries to resolve the same package
            let package = if i % 2 == 0 { "lodash" } else { "chalk" };
            installer.fetch_package_info(package).await
        })
    }).collect();
    
    let start = Instant::now();
    let results = join_all(tasks).await;
    let duration = start.elapsed();
    
    // All tasks should complete successfully
    let mut success_count = 0;
    for result in results {
        if let Ok(Ok(_)) = result {
            success_count += 1;
        }
    }
    
    assert!(success_count >= 8, "At least 8/10 concurrent cache accesses should succeed");
    assert!(duration.as_secs() < 15, "Concurrent cache access should complete quickly");
    
    println!("✅ Concurrent cache access: {}/10 successful in {:.2}s", success_count, duration.as_secs_f64());
}

#[test]
async fn test_memory_pressure_scenarios() {
    // Test behavior under memory pressure
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Simulate memory pressure by resolving many packages simultaneously
    let packages: Vec<String> = (0..50).map(|i| format!("test-package-{}", i)).collect();
    
    let start = Instant::now();
    let result = installer.resolve_dependencies(&packages).await;
    let duration = start.elapsed();
    
    // Should handle gracefully even if some packages don't exist
    match result {
        Ok(resolved) => {
            println!("✅ Memory pressure test: resolved {} packages", resolved.len());
        }
        Err(e) => {
            println!("✅ Memory pressure handled gracefully: {}", e);
        }
    }
    
    assert!(duration.as_secs() < 60, "Should complete within 60 seconds under memory pressure");
}

#[test]
async fn test_filesystem_permission_errors() {
    // Test handling of filesystem permission errors
    let temp_dir = TempDir::new().expect("Should create temp dir");
    
    // Try to create installer with restricted permissions
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test basic operations that might encounter permission issues
    let result = installer.fetch_package_info("lodash").await;
    
    match result {
        Ok(_) => {
            println!("✅ Filesystem operations successful");
        }
        Err(e) => {
            println!("✅ Filesystem permission error handled: {}", e);
        }
    }
}

#[test]
async fn test_registry_unavailable_scenarios() {
    // Test behavior when registry is unavailable
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test with a package that might cause registry issues
    let start = Instant::now();
    let result = installer.fetch_package_info("definitely-non-existent-package-12345").await;
    let duration = start.elapsed();
    
    assert!(result.is_err(), "Should fail for non-existent package");
    assert!(duration.as_secs() < 30, "Should fail quickly");
    
    // Error should be informative
    let error_msg = result.unwrap_err().to_string();
    assert!(error_msg.contains("not found") || error_msg.contains("404") || error_msg.contains("Network"), 
        "Error should be informative: {}", error_msg);
    
    println!("✅ Registry unavailable scenario handled in {:.2}s", duration.as_secs_f64());
}

#[test]
async fn test_package_integrity_validation() {
    // Test package integrity validation
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test with a known good package
    let result = installer.fetch_package_info("lodash").await;
    
    match result {
        Ok(package) => {
            // Validate package structure
            assert!(!package.name.is_empty(), "Package name should not be empty");
            assert!(!package.version.is_empty(), "Package version should not be empty");
            assert!(!package.tarball_url.is_empty(), "Tarball URL should not be empty");
            assert!(package.tarball_url.starts_with("http"), "Tarball URL should be valid HTTP URL");
            
            println!("✅ Package integrity validation passed for {}", package.name);
        }
        Err(e) => {
            println!("✅ Package integrity error handled: {}", e);
        }
    }
}

#[test]
async fn test_unicode_and_special_characters() {
    // Test handling of Unicode and special characters in package names
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    let special_names = vec![
        "package-with-unicode-🚀".to_string(),
        "package.with.dots".to_string(),
        "package_with_underscores".to_string(),
        "@scoped/package".to_string(),
    ];
    
    for name in special_names {
        let result = installer.fetch_package_info(&name).await;
        
        // Should handle gracefully (either succeed or fail with clear error)
        match result {
            Ok(_) => {
                println!("✅ Unicode package '{}' resolved successfully", name);
            }
            Err(e) => {
                println!("✅ Unicode package '{}' error handled: {}", name, e);
            }
        }
    }
}

#[test]
async fn test_rate_limiting_behavior() {
    // Test behavior under rate limiting
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Make many rapid requests to test rate limiting
    let mut tasks = Vec::new();
    for i in 0..20 {
        let installer = installer.clone();
        tasks.push(tokio::spawn(async move {
            installer.fetch_package_info(&format!("test-package-{}", i)).await
        }));
    }
    
    let start = Instant::now();
    let results = futures_util::future::join_all(tasks).await;
    let duration = start.elapsed();
    
    let mut success_count = 0;
    let mut rate_limited_count = 0;
    
    for result in results {
        match result {
            Ok(Ok(_)) => success_count += 1,
            Ok(Err(e)) => {
                if e.to_string().contains("rate") || e.to_string().contains("limit") {
                    rate_limited_count += 1;
                }
            }
            Err(_) => {} // Task panic
        }
    }
    
    println!("✅ Rate limiting test: {} successful, {} rate limited in {:.2}s", 
        success_count, rate_limited_count, duration.as_secs_f64());
    
    // Should handle rate limiting gracefully
    assert!(duration.as_secs() < 60, "Should complete within reasonable time even with rate limiting");
}

#[test]
async fn test_configuration_edge_cases() {
    // Test edge cases in configuration
    let config = nx::config::get_config();
    
    // Test configuration validation
    let validation_result = config.validate();
    assert!(validation_result.is_ok(), "Default configuration should be valid");
    
    // Test configuration with edge case values
    let mut test_config = config.clone();
    test_config.network.timeout_seconds = 0; // Invalid timeout
    
    let validation_result = test_config.validate();
    assert!(validation_result.is_err(), "Invalid configuration should fail validation");
    
    println!("✅ Configuration edge cases handled correctly");
}

impl Clone for nx::installer::NpmInstaller {
    fn clone(&self) -> Self {
        Self {
            network_client: self.network_client.clone(),
            node_modules_dir: self.node_modules_dir.clone(),
        }
    }
}
