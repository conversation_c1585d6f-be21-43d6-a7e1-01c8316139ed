{"version": 3, "file": "ElementHandle.js", "sourceRoot": "", "sources": ["../../../../src/cdp/ElementHandle.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKH,8DAIiC;AAEjC,+CAA6C;AAC7C,sDAA8C;AAC9C,iDAAyC;AACzC,uEAA+D;AAC/D,yDAAsD;AAKtD,+CAA0C;AAE1C,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC;AAExE;;;;;;GAMG;IACU,gBAAgB;sBAEnB,gCAAa;;;;;;iBAFV,gBAEX,SAAQ,WAA0B;;;wCAmCjC,IAAA,+BAAe,GAAE;0CAWjB,IAAA,+BAAe,GAAE,EACjB,qCAAkB;sCAgBlB,IAAA,+BAAe,GAAE,EACjB,qCAAkB;oCA4DlB,IAAA,+BAAe,GAAE;YAxFlB,uLAAe,YAAY,6DAQ1B;YAID,6LAAe,cAAc,6DAa5B;YAID,iLAAe,UAAU,6DAyDxB;YAGD,2KAAe,QAAQ,6DAWtB;;;QAtID,cAAc,GAJH,mDAAgB,CAIH;QAExB,YACE,KAAoB,EACpB,YAA2C;YAE3C,KAAK,CAAC,IAAI,yBAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,IAAa,KAAK;YAChB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QAC3B,CAAC;QAED,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAC5B,CAAC;QAEQ,YAAY;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;QACpC,CAAC;QAED,IAAI,aAAa;YACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;QAClC,CAAC;QAED,IAAa,KAAK;YAChB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAuB,CAAC;QAC5C,CAAC;QAOQ,KAAK,CAAC,YAAY;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC1D,QAAQ,EAAE,IAAI,CAAC,EAAE;aAClB,CAAC,CAAC;YACH,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzD,CAAC;QAIQ,KAAK,CAAC,cAAc;YAG3B,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;oBACnD,QAAQ,EAAE,IAAI,CAAC,EAAE;iBAClB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;gBAClB,oFAAoF;gBACpF,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QAIQ,KAAK,CAAC,UAAU,CAEvB,GAAG,KAAe;YAElB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC/C,OAAO,OAAO,CAAC,QAAQ,CAAC;YAC1B,CAAC,CAAC,CAAC;YACH,IAAA,kBAAM,EACJ,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,UAAU,EAC/B,iEAAiE,CAClE,CAAC;YAEF,gDAAgD;YAChD,MAAM,IAAI,GAAG,4BAAW,CAAC,KAAK,CAAC,IAAI,CAAC;YACpC,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;oBAC3B,IACE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;wBAC/B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAC/B,CAAC;wBACD,OAAO,QAAQ,CAAC;oBAClB,CAAC;yBAAM,CAAC;wBACN,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAChC,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED;;;;eAIG;YACH,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,wEAAwE;gBACxE,8BAA8B;gBAC9B,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;oBAC5B,OAAO,CAAC,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC,KAAK,CAAC;oBAEzC,gFAAgF;oBAChF,OAAO,CAAC,aAAa,CACnB,IAAI,KAAK,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CACpD,CAAC;oBACF,OAAO,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,EACJ,IAAI,EAAE,EAAC,aAAa,EAAC,GACtB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,QAAQ,EAAE,IAAI,CAAC,EAAE;aAClB,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC9C,QAAQ,EAAE,IAAI,CAAC,EAAE;gBACjB,KAAK;gBACL,aAAa;aACd,CAAC,CAAC;QACL,CAAC;QAGQ,KAAK,CAAC,QAAQ,CAAC,IAAkB;YACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC1D,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;YAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAC/B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACzC,OAAO;gBACP,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,UAAU;aACtB,CAAC,CAAC;QACL,CAAC;QAEQ,KAAK,CAAC,CAAC,WAAW,CACzB,IAAyB,EACzB,IAAyB;YAEzB,MAAM,EAAC,KAAK,EAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBAClE,QAAQ,EAAE,IAAI,CAAC,EAAE;gBACjB,cAAc,EAAE,IAAI;gBACpB,IAAI;aACL,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBAClC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACf,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,IAAI,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChD,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,CAAC,wCAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;gBAClD,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAEvD,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAEQ,KAAK,CAAC,aAAa;YAC1B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,cAAc,CAAC;YAC7B,CAAC;YACD,MAAM,EAAC,IAAI,EAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACxD,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;YACzC,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;;;AAnLU,4CAAgB"}