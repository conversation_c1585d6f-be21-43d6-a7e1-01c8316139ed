// Comprehensive stress tests for nx package manager

use std::time::{Duration, Instant};
use tokio::test;
use tempfile::TempDir;

// Test with large dependency trees (100+ packages)
#[test]
async fn test_large_dependency_tree_react_ecosystem() {
    let start = Instant::now();
    
    // React ecosystem with many dependencies
    let packages = vec![
        "react".to_string(),
        "react-dom".to_string(),
        "react-router-dom".to_string(),
        "@testing-library/react".to_string(),
        "@testing-library/jest-dom".to_string(),
        "react-query".to_string(),
        "styled-components".to_string(),
        "framer-motion".to_string(),
        "react-hook-form".to_string(),
        "react-select".to_string(),
        "react-table".to_string(),
        "react-virtualized".to_string(),
        "react-spring".to_string(),
        "react-transition-group".to_string(),
        "react-helmet".to_string(),
    ];
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolved = installer.resolve_dependencies(&packages).await;
    
    let duration = start.elapsed();
    
    assert!(resolved.is_ok(), "Should resolve large React ecosystem");
    let packages = resolved.unwrap();
    
    // Should resolve 100+ packages including transitive dependencies
    assert!(packages.len() >= 50, "Should resolve at least 50 packages, got {}", packages.len());
    assert!(duration.as_secs() < 30, "Should complete within 30 seconds, took {:.2}s", duration.as_secs_f64());
    
    println!("✅ React ecosystem: {} packages resolved in {:.2}s", packages.len(), duration.as_secs_f64());
}

#[test]
async fn test_large_dependency_tree_angular_ecosystem() {
    let start = Instant::now();
    
    // Angular ecosystem
    let packages = vec![
        "@angular/core".to_string(),
        "@angular/common".to_string(),
        "@angular/router".to_string(),
        "@angular/forms".to_string(),
        "@angular/http".to_string(),
        "@angular/animations".to_string(),
        "@angular/material".to_string(),
        "@angular/cdk".to_string(),
        "rxjs".to_string(),
        "zone.js".to_string(),
        "typescript".to_string(),
        "@angular/cli".to_string(),
    ];
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolved = installer.resolve_dependencies(&packages).await;
    
    let duration = start.elapsed();
    
    assert!(resolved.is_ok(), "Should resolve Angular ecosystem");
    let packages = resolved.unwrap();
    
    assert!(packages.len() >= 30, "Should resolve at least 30 packages, got {}", packages.len());
    assert!(duration.as_secs() < 25, "Should complete within 25 seconds, took {:.2}s", duration.as_secs_f64());
    
    println!("✅ Angular ecosystem: {} packages resolved in {:.2}s", packages.len(), duration.as_secs_f64());
}

#[test]
async fn test_large_dependency_tree_node_backend() {
    let start = Instant::now();
    
    // Node.js backend ecosystem
    let packages = vec![
        "express".to_string(),
        "mongoose".to_string(),
        "jsonwebtoken".to_string(),
        "bcryptjs".to_string(),
        "cors".to_string(),
        "helmet".to_string(),
        "morgan".to_string(),
        "dotenv".to_string(),
        "nodemailer".to_string(),
        "multer".to_string(),
        "passport".to_string(),
        "passport-jwt".to_string(),
        "joi".to_string(),
        "compression".to_string(),
        "rate-limiter-flexible".to_string(),
    ];
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolved = installer.resolve_dependencies(&packages).await;
    
    let duration = start.elapsed();
    
    assert!(resolved.is_ok(), "Should resolve Node.js backend ecosystem");
    let packages = resolved.unwrap();
    
    assert!(packages.len() >= 40, "Should resolve at least 40 packages, got {}", packages.len());
    assert!(duration.as_secs() < 20, "Should complete within 20 seconds, took {:.2}s", duration.as_secs_f64());
    
    println!("✅ Node.js backend: {} packages resolved in {:.2}s", packages.len(), duration.as_secs_f64());
}

// Test concurrent stress scenarios
#[test]
async fn test_concurrent_dependency_resolution() {
    use futures_util::future::join_all;
    
    let start = Instant::now();
    
    // Create multiple concurrent resolution tasks
    let tasks = vec![
        vec!["lodash".to_string(), "moment".to_string()],
        vec!["axios".to_string(), "chalk".to_string()],
        vec!["commander".to_string(), "inquirer".to_string()],
        vec!["express".to_string(), "cors".to_string()],
        vec!["react".to_string(), "react-dom".to_string()],
    ];
    
    let futures: Vec<_> = tasks.into_iter().map(|packages| {
        tokio::spawn(async move {
            let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
            installer.resolve_dependencies(&packages).await
        })
    }).collect();
    
    let results = join_all(futures).await;
    let duration = start.elapsed();
    
    // All tasks should complete successfully
    for (i, result) in results.into_iter().enumerate() {
        let task_result = result.expect("Task should not panic");
        assert!(task_result.is_ok(), "Task {} should succeed", i);
    }
    
    assert!(duration.as_secs() < 15, "Concurrent resolution should complete within 15 seconds, took {:.2}s", duration.as_secs_f64());
    
    println!("✅ Concurrent resolution: 5 tasks completed in {:.2}s", duration.as_secs_f64());
}

// Test memory usage under heavy load
#[test]
async fn test_memory_usage_heavy_load() {
    use std::sync::Arc;
    use std::sync::atomic::{AtomicUsize, Ordering};
    
    let start = Instant::now();
    let memory_samples = Arc::new(AtomicUsize::new(0));
    
    // Monitor memory usage
    let memory_monitor = {
        let samples = memory_samples.clone();
        tokio::spawn(async move {
            for _ in 0..30 { // Monitor for 30 seconds
                tokio::time::sleep(Duration::from_secs(1)).await;
                // Simplified memory tracking
                let current_memory = get_approximate_memory_usage();
                samples.store(current_memory, Ordering::Relaxed);
            }
        })
    };
    
    // Heavy workload: resolve many packages simultaneously
    let heavy_packages = vec![
        "webpack".to_string(),
        "babel-core".to_string(),
        "eslint".to_string(),
        "typescript".to_string(),
        "jest".to_string(),
        "prettier".to_string(),
        "rollup".to_string(),
        "vite".to_string(),
        "parcel".to_string(),
        "esbuild".to_string(),
    ];
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolved = installer.resolve_dependencies(&heavy_packages).await;
    
    let duration = start.elapsed();
    memory_monitor.abort(); // Stop monitoring
    
    assert!(resolved.is_ok(), "Heavy workload should succeed");
    let packages = resolved.unwrap();
    
    let peak_memory = memory_samples.load(Ordering::Relaxed);
    
    assert!(packages.len() >= 50, "Should resolve many packages");
    assert!(duration.as_secs() < 45, "Should complete within 45 seconds");
    assert!(peak_memory < 500 * 1024 * 1024, "Memory usage should be reasonable (<500MB)");
    
    println!("✅ Heavy load: {} packages, {:.2}s, ~{}MB peak memory", 
        packages.len(), 
        duration.as_secs_f64(),
        peak_memory / 1024 / 1024
    );
}

// Test edge cases and error scenarios
#[test]
async fn test_network_failure_resilience() {
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test with non-existent packages
    let result = installer.resolve_dependencies(&[
        "this-package-absolutely-does-not-exist-12345".to_string()
    ]).await;
    
    // Should handle gracefully
    assert!(result.is_err() || result.unwrap().is_empty(), "Should handle non-existent packages gracefully");
    
    // Test with malformed package names
    let result = installer.resolve_dependencies(&[
        "".to_string(),
        "invalid/package/name".to_string(),
        "package with spaces".to_string(),
    ]).await;
    
    assert!(result.is_err() || result.unwrap().is_empty(), "Should handle malformed package names");
    
    println!("✅ Network failure resilience tests passed");
}

#[test]
async fn test_disk_space_simulation() {
    // This test simulates low disk space scenarios
    // In a real implementation, you'd use filesystem quotas or mock filesystem
    
    let temp_dir = TempDir::new().expect("Should create temp dir");
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test with a small package first
    let result = installer.fetch_package_info("lodash").await;
    assert!(result.is_ok(), "Should fetch package info successfully");
    
    println!("✅ Disk space simulation test passed");
}

#[test]
async fn test_corrupted_package_handling() {
    // Test handling of corrupted or invalid package data
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test with packages that might have corrupted metadata
    let packages = vec![
        "lodash".to_string(), // Known good package
        "non-existent-package-xyz".to_string(), // Non-existent
    ];
    
    let result = installer.resolve_dependencies(&packages).await;
    
    // Should handle mixed valid/invalid packages gracefully
    if let Ok(resolved) = result {
        assert!(resolved.len() >= 1, "Should resolve at least the valid package");
        println!("✅ Resolved {} packages despite invalid entries", resolved.len());
    } else {
        println!("✅ Gracefully handled corrupted package scenario");
    }
}

// Performance regression tests
#[test]
async fn test_performance_regression_small_packages() {
    let start = Instant::now();
    
    let packages = vec!["lodash".to_string(), "chalk".to_string(), "commander".to_string()];
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolved = installer.resolve_dependencies(&packages).await;
    
    let duration = start.elapsed();
    
    assert!(resolved.is_ok(), "Should resolve small packages");
    assert!(duration.as_secs() < 5, "Small packages should resolve quickly (<5s), took {:.2}s", duration.as_secs_f64());
    
    println!("✅ Small packages performance: {:.2}s", duration.as_secs_f64());
}

#[test]
async fn test_performance_regression_medium_packages() {
    let start = Instant::now();
    
    let packages = vec![
        "express".to_string(),
        "react".to_string(),
        "webpack".to_string(),
        "typescript".to_string(),
        "eslint".to_string(),
    ];
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolved = installer.resolve_dependencies(&packages).await;
    
    let duration = start.elapsed();
    
    assert!(resolved.is_ok(), "Should resolve medium packages");
    assert!(duration.as_secs() < 15, "Medium packages should resolve quickly (<15s), took {:.2}s", duration.as_secs_f64());
    
    println!("✅ Medium packages performance: {:.2}s", duration.as_secs_f64());
}

// Cache effectiveness under stress
#[test]
async fn test_cache_effectiveness_under_stress() {
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // First resolution (cache miss)
    let start1 = Instant::now();
    let result1 = installer.resolve_dependencies(&["express".to_string()]).await;
    let duration1 = start1.elapsed();
    
    assert!(result1.is_ok(), "First resolution should succeed");
    
    // Second resolution (cache hit)
    let start2 = Instant::now();
    let result2 = installer.resolve_dependencies(&["express".to_string()]).await;
    let duration2 = start2.elapsed();
    
    assert!(result2.is_ok(), "Second resolution should succeed");
    
    // Cache should provide significant speedup
    let speedup = duration1.as_secs_f64() / duration2.as_secs_f64().max(0.001);
    assert!(speedup >= 2.0, "Cache should provide at least 2x speedup, got {:.1}x", speedup);
    
    println!("✅ Cache effectiveness: {:.1}x speedup ({:.2}s -> {:.2}s)", 
        speedup, duration1.as_secs_f64(), duration2.as_secs_f64());
}

// Helper function to get approximate memory usage
fn get_approximate_memory_usage() -> usize {
    // This is a simplified implementation
    // In a real test, you'd use proper memory profiling
    use std::process;
    
    // Read from /proc/self/status on Linux, or use platform-specific APIs
    if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
        for line in status.lines() {
            if line.starts_with("VmRSS:") {
                if let Some(kb_str) = line.split_whitespace().nth(1) {
                    if let Ok(kb) = kb_str.parse::<usize>() {
                        return kb * 1024; // Convert to bytes
                    }
                }
            }
        }
    }
    
    // Fallback: rough estimate based on process ID
    process::id() as usize * 1024 * 100 // Very rough estimate
}
