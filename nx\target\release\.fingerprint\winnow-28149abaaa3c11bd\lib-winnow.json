{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 11708846982463781553, "path": 11475697647774606622, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\winnow-28149abaaa3c11bd\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}