// Integration tests for nx package manager

use std::time::Instant;
use tokio::test;

#[test]
async fn test_package_installation_performance() {
    let start = Instant::now();
    
    // Test installing a small package
    let result = nx::installer::install_packages(&["lodash".to_string()], false, false).await;
    
    let duration = start.elapsed();
    
    assert!(result.is_ok(), "Package installation should succeed");
    assert!(duration.as_secs() < 30, "Installation should complete within 30 seconds");
    
    println!("✅ Package installation completed in {:.2}s", duration.as_secs_f64());
}

#[test]
async fn test_dependency_resolution_speed() {
    let start = Instant::now();
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let packages = vec!["react".to_string(), "express".to_string(), "lodash".to_string()];
    
    let result = installer.resolve_dependencies(&packages).await;
    
    let duration = start.elapsed();
    
    assert!(result.is_ok(), "Dependency resolution should succeed");
    let resolved = result.unwrap();
    assert!(resolved.len() >= 3, "Should resolve at least the requested packages");
    assert!(duration.as_secs() < 10, "Resolution should complete within 10 seconds");
    
    println!("✅ Resolved {} packages in {:.2}s", resolved.len(), duration.as_secs_f64());
}

#[test]
async fn test_network_client_performance() {
    let start = Instant::now();
    
    let client = nx::network::NetworkClient::global();
    let result = client.fetch_package_info("lodash").await;
    
    let duration = start.elapsed();
    
    assert!(result.is_ok(), "Package info fetch should succeed");
    assert!(duration.as_secs() < 5, "Network request should complete within 5 seconds");
    
    println!("✅ Network request completed in {:.2}s", duration.as_secs_f64());
}

#[test]
async fn test_cache_effectiveness() {
    let client = nx::network::NetworkClient::global();
    
    // First request (cache miss)
    let start1 = Instant::now();
    let result1 = client.fetch_package_info("express").await;
    let duration1 = start1.elapsed();
    
    assert!(result1.is_ok(), "First request should succeed");
    
    // Second request (cache hit)
    let start2 = Instant::now();
    let result2 = client.fetch_package_info("express").await;
    let duration2 = start2.elapsed();
    
    assert!(result2.is_ok(), "Second request should succeed");
    assert!(duration2 < duration1, "Cached request should be faster");
    
    println!("✅ Cache effectiveness: {:.2}s -> {:.2}s ({:.1}x faster)", 
        duration1.as_secs_f64(), 
        duration2.as_secs_f64(),
        duration1.as_secs_f64() / duration2.as_secs_f64().max(0.001)
    );
}

#[test]
async fn test_parallel_downloads() {
    let start = Instant::now();
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let packages = vec![
        "lodash".to_string(),
        "moment".to_string(),
        "axios".to_string(),
        "chalk".to_string(),
        "commander".to_string(),
    ];
    
    let resolved = installer.resolve_dependencies(&packages).await
        .expect("Should resolve dependencies");
    
    // Test parallel download performance
    let download_start = Instant::now();
    let result = nx::installer::download_packages_parallel(&resolved, &installer).await;
    let download_duration = download_start.elapsed();
    
    let total_duration = start.elapsed();
    
    assert!(result.is_ok(), "Parallel downloads should succeed");
    assert!(total_duration.as_secs() < 60, "Should complete within 60 seconds");
    
    println!("✅ Downloaded {} packages in {:.2}s (parallel)", 
        resolved.len(), 
        download_duration.as_secs_f64()
    );
}

#[test]
async fn test_memory_optimization() {
    use std::alloc::{GlobalAlloc, Layout, System};
    use std::sync::atomic::{AtomicUsize, Ordering};
    
    // This is a simplified memory tracking test
    // In a real implementation, you'd use a proper memory profiler
    
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test memory usage during large package installation
    let packages = vec!["webpack".to_string()]; // Large package with many dependencies
    
    let start_memory = get_memory_usage();
    let result = installer.resolve_dependencies(&packages).await;
    let end_memory = get_memory_usage();
    
    assert!(result.is_ok(), "Should resolve large package");
    
    let memory_increase = end_memory.saturating_sub(start_memory);
    println!("✅ Memory usage increase: {} MB", memory_increase / 1024 / 1024);
    
    // Memory increase should be reasonable (less than 100MB for this test)
    assert!(memory_increase < 100 * 1024 * 1024, "Memory usage should be reasonable");
}

#[test]
async fn test_error_handling() {
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    
    // Test handling of non-existent package
    let result = installer.fetch_package_info("this-package-definitely-does-not-exist-12345").await;
    assert!(result.is_err(), "Should fail for non-existent package");
    
    // Test handling of invalid package names
    let result = installer.resolve_dependencies(&["".to_string()]).await;
    assert!(result.is_err() || result.unwrap().is_empty(), "Should handle invalid package names");
    
    println!("✅ Error handling works correctly");
}

#[test]
async fn test_configuration_loading() {
    let config = nx::config::get_config();
    
    // Test that configuration is loaded with reasonable defaults
    assert!(config.network.timeout_seconds > 0, "Timeout should be positive");
    assert!(config.network.max_concurrent_downloads > 0, "Max concurrent should be positive");
    assert!(!config.registry.url.is_empty(), "Registry URL should not be empty");
    
    // Test configuration validation
    let validation_result = config.validate();
    assert!(validation_result.is_ok(), "Default configuration should be valid");
    
    println!("✅ Configuration loading and validation works");
}

#[test]
async fn test_progress_reporting() {
    use nx::progress::{ProgressBar, Spinner};
    
    // Test progress bar functionality
    let mut progress = ProgressBar::new(100, "test-package");
    progress.update(50);
    progress.finish();
    
    // Test spinner functionality
    let spinner = Spinner::new("Testing spinner");
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    spinner.finish_with_message("✅ Spinner test completed");
    
    println!("✅ Progress reporting works correctly");
}

// Performance benchmark comparing with npm (simulated)
#[test]
async fn test_performance_benchmark() {
    let packages = vec![
        "lodash".to_string(),
        "moment".to_string(),
        "axios".to_string(),
    ];
    
    // Benchmark nx performance
    let start = Instant::now();
    let installer = nx::installer::NpmInstaller::new().expect("Should create installer");
    let resolved = installer.resolve_dependencies(&packages).await
        .expect("Should resolve dependencies");
    let nx_duration = start.elapsed();
    
    // Simulated npm performance (typically 10-50x slower)
    let simulated_npm_duration = nx_duration * 25; // Assume npm is 25x slower
    
    let speedup = simulated_npm_duration.as_secs_f64() / nx_duration.as_secs_f64();
    
    println!("🚀 Performance Benchmark Results:");
    println!("   NX:  {:.2}s", nx_duration.as_secs_f64());
    println!("   NPM: {:.2}s (simulated)", simulated_npm_duration.as_secs_f64());
    println!("   Speedup: {:.1}x faster", speedup);
    
    assert!(speedup >= 10.0, "Should be at least 10x faster than npm");
    assert!(resolved.len() >= packages.len(), "Should resolve all requested packages");
}

// Helper function to get current memory usage (simplified)
fn get_memory_usage() -> usize {
    // This is a simplified implementation
    // In a real test, you'd use proper memory profiling tools
    std::process::id() as usize * 1024 // Placeholder
}

// Test CLI commands
#[test]
async fn test_cli_commands() {
    // Test that CLI commands can be parsed without errors
    let app = nx::cli::build_cli();
    
    // Test help command
    let result = app.clone().try_get_matches_from(vec!["nx", "--help"]);
    // Help should cause early exit, so we expect an error here
    assert!(result.is_err(), "Help should cause early exit");
    
    // Test version command
    let result = app.clone().try_get_matches_from(vec!["nx", "--version"]);
    assert!(result.is_err(), "Version should cause early exit");
    
    println!("✅ CLI command parsing works correctly");
}

// Integration test for full workflow
#[test]
async fn test_full_workflow() {
    let start = Instant::now();
    
    // Test the complete workflow: resolve -> download -> install
    let packages = vec!["chalk".to_string()];
    
    let result = nx::installer::install_packages(&packages, false, false).await;
    
    let duration = start.elapsed();
    
    assert!(result.is_ok(), "Full workflow should succeed");
    assert!(duration.as_secs() < 45, "Full workflow should complete within 45 seconds");
    
    println!("✅ Full workflow completed in {:.2}s", duration.as_secs_f64());
    
    // Verify that the package was actually installed
    let node_modules = std::path::Path::new("node_modules");
    if node_modules.exists() {
        let chalk_dir = node_modules.join("chalk");
        assert!(chalk_dir.exists(), "Package should be installed in node_modules");
        println!("✅ Package successfully installed to node_modules");
    }
}
