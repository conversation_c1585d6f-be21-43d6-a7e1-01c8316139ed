{"rustc": 1842507548689473721, "features": "[\"default\", \"improved_unicode\", \"unicode-segmentation\", \"unicode-width\"]", "declared_features": "[\"default\", \"futures\", \"improved_unicode\", \"in_memory\", \"rayon\", \"tokio\", \"unicode-segmentation\", \"unicode-width\", \"vt100\"]", "target": 10301169156738538477, "profile": 15625788057656754760, "path": 10564265116035168945, "deps": [[1232198224951696867, "unicode_segmentation", false, 4989759256770569151], [3958489542916937055, "portable_atomic", false, 11287302066582379728], [11485413305714879807, "console", false, 18254060407599436736], [13774335185398496026, "unicode_width", false, 12286758612505102472], [14188466555567159420, "number_prefix", false, 407260176633719347]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\indicatif-1b045e3c8fb2df5c\\dep-lib-indicatif", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}