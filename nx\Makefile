# Makefile for nx package manager production builds

.PHONY: all build release test bench clean install help
.DEFAULT_GOAL := help

# Configuration
CARGO := cargo
VERSION := $(shell $(CARGO) metadata --no-deps --format-version 1 | jq -r '.packages[0].version')
BUILD_DIR := target/release-builds
INSTALL_DIR := /usr/local/bin

# Colors
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m

help: ## Show this help message
	@echo "$(BLUE)🚀 NX Package Manager Build System$(NC)"
	@echo "$(BLUE)====================================$(NC)"
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)Version: $(VERSION)$(NC)"

all: clean test build ## Clean, test, and build everything

build: ## Build optimized release binary
	@echo "$(BLUE)🔨 Building optimized release binary...$(NC)"
	RUSTFLAGS="-C target-cpu=native" $(CARGO) build --release
	@echo "$(GREEN)✅ Build completed$(NC)"

release: ## Build production releases for all platforms
	@echo "$(BLUE)🚀 Building production releases...$(NC)"
	@chmod +x scripts/build-release.sh
	@./scripts/build-release.sh $(VERSION)

pgo: ## Build with Profile-Guided Optimization
	@echo "$(BLUE)🎯 Building with Profile-Guided Optimization...$(NC)"
	@echo "$(YELLOW)Step 1: Building instrumented binary...$(NC)"
	RUSTFLAGS="-Cprofile-generate=./pgo-data" $(CARGO) build --release
	@echo "$(YELLOW)Step 2: Running training workload...$(NC)"
	@mkdir -p pgo-data
	@./target/release/nx --version > /dev/null || true
	@./target/release/nx install --help > /dev/null || true
	@echo "$(YELLOW)Step 3: Building optimized binary...$(NC)"
	RUSTFLAGS="-Cprofile-use=./pgo-data" $(CARGO) build --release
	@rm -rf pgo-data
	@echo "$(GREEN)✅ PGO build completed$(NC)"

test: ## Run all tests
	@echo "$(BLUE)🧪 Running tests...$(NC)"
	$(CARGO) test --all-features
	@echo "$(GREEN)✅ All tests passed$(NC)"

test-integration: ## Run integration tests
	@echo "$(BLUE)🔬 Running integration tests...$(NC)"
	$(CARGO) test --test integration_tests
	@echo "$(GREEN)✅ Integration tests passed$(NC)"

bench: ## Run performance benchmarks
	@echo "$(BLUE)📊 Running performance benchmarks...$(NC)"
	$(CARGO) bench
	@echo "$(GREEN)✅ Benchmarks completed$(NC)"

stress-test: build ## Run stress tests with large dependency trees
	@echo "$(BLUE)💪 Running stress tests...$(NC)"
	@echo "$(YELLOW)Testing with React ecosystem (100+ packages)...$(NC)"
	@./target/release/nx install react react-dom react-router-dom @testing-library/react --dry-run || true
	@echo "$(YELLOW)Testing with Angular ecosystem...$(NC)"
	@./target/release/nx install @angular/core @angular/common @angular/router --dry-run || true
	@echo "$(GREEN)✅ Stress tests completed$(NC)"

memory-test: build ## Test memory usage under load
	@echo "$(BLUE)🧠 Testing memory usage...$(NC)"
	@if command -v valgrind >/dev/null 2>&1; then \
		echo "$(YELLOW)Running with Valgrind...$(NC)"; \
		valgrind --tool=massif --massif-out-file=massif.out ./target/release/nx --version; \
		ms_print massif.out | head -20; \
		rm -f massif.out; \
	else \
		echo "$(YELLOW)Valgrind not available, running basic memory test...$(NC)"; \
		/usr/bin/time -v ./target/release/nx --version 2>&1 | grep -E "(Maximum resident|Peak memory)" || true; \
	fi
	@echo "$(GREEN)✅ Memory test completed$(NC)"

perf-test: build ## Run performance comparison against npm
	@echo "$(BLUE)⚡ Running performance comparison...$(NC)"
	@echo "$(YELLOW)Testing nx performance...$(NC)"
	@time ./target/release/nx install lodash --dry-run 2>/dev/null || true
	@echo "$(YELLOW)Testing npm performance (if available)...$(NC)"
	@if command -v npm >/dev/null 2>&1; then \
		time npm install lodash --dry-run 2>/dev/null || true; \
	else \
		echo "$(YELLOW)npm not available for comparison$(NC)"; \
	fi
	@echo "$(GREEN)✅ Performance test completed$(NC)"

security-audit: ## Run security audit
	@echo "$(BLUE)🔒 Running security audit...$(NC)"
	$(CARGO) audit
	@echo "$(GREEN)✅ Security audit completed$(NC)"

lint: ## Run linting and formatting checks
	@echo "$(BLUE)🧹 Running linting...$(NC)"
	$(CARGO) fmt --check
	$(CARGO) clippy --all-targets --all-features -- -D warnings
	@echo "$(GREEN)✅ Linting completed$(NC)"

format: ## Format code
	@echo "$(BLUE)✨ Formatting code...$(NC)"
	$(CARGO) fmt
	@echo "$(GREEN)✅ Code formatted$(NC)"

clean: ## Clean build artifacts
	@echo "$(BLUE)🧹 Cleaning build artifacts...$(NC)"
	$(CARGO) clean
	@rm -rf $(BUILD_DIR)
	@rm -rf pgo-data
	@echo "$(GREEN)✅ Clean completed$(NC)"

install: build ## Install nx to system
	@echo "$(BLUE)📦 Installing nx to $(INSTALL_DIR)...$(NC)"
	@sudo cp target/release/nx $(INSTALL_DIR)/
	@sudo chmod +x $(INSTALL_DIR)/nx
	@echo "$(GREEN)✅ nx installed to $(INSTALL_DIR)$(NC)"
	@echo "$(YELLOW)Run 'nx --version' to verify installation$(NC)"

uninstall: ## Uninstall nx from system
	@echo "$(BLUE)🗑️  Uninstalling nx...$(NC)"
	@sudo rm -f $(INSTALL_DIR)/nx
	@echo "$(GREEN)✅ nx uninstalled$(NC)"

docker-build: ## Build Docker image
	@echo "$(BLUE)🐳 Building Docker image...$(NC)"
	@docker build -t nx-package-manager:$(VERSION) .
	@docker build -t nx-package-manager:latest .
	@echo "$(GREEN)✅ Docker image built$(NC)"

size-check: build ## Check binary size
	@echo "$(BLUE)📏 Checking binary size...$(NC)"
	@ls -lh target/release/nx
	@echo "$(YELLOW)Stripped size:$(NC)"
	@strip target/release/nx 2>/dev/null || true
	@ls -lh target/release/nx
	@echo "$(GREEN)✅ Size check completed$(NC)"

deps-check: ## Check dependency tree
	@echo "$(BLUE)🌳 Checking dependency tree...$(NC)"
	$(CARGO) tree
	@echo "$(GREEN)✅ Dependency check completed$(NC)"

update-deps: ## Update dependencies
	@echo "$(BLUE)⬆️  Updating dependencies...$(NC)"
	$(CARGO) update
	@echo "$(GREEN)✅ Dependencies updated$(NC)"

ci: clean lint test bench ## Run CI pipeline
	@echo "$(BLUE)🤖 Running CI pipeline...$(NC)"
	@echo "$(GREEN)✅ CI pipeline completed$(NC)"

release-check: ## Verify release readiness
	@echo "$(BLUE)🔍 Checking release readiness...$(NC)"
	@echo "$(YELLOW)Version: $(VERSION)$(NC)"
	@echo "$(YELLOW)Checking Cargo.toml...$(NC)"
	@grep -q "version = \"$(VERSION)\"" Cargo.toml && echo "$(GREEN)✅ Version matches$(NC)" || echo "$(RED)❌ Version mismatch$(NC)"
	@echo "$(YELLOW)Checking for TODO/FIXME...$(NC)"
	@! grep -r "TODO\|FIXME" src/ && echo "$(GREEN)✅ No TODO/FIXME found$(NC)" || echo "$(YELLOW)⚠️  TODO/FIXME found$(NC)"
	@echo "$(YELLOW)Checking tests...$(NC)"
	@$(CARGO) test --quiet && echo "$(GREEN)✅ Tests pass$(NC)" || echo "$(RED)❌ Tests fail$(NC)"
	@echo "$(GREEN)✅ Release check completed$(NC)"

# Platform-specific targets
linux: ## Build for Linux
	@echo "$(BLUE)🐧 Building for Linux...$(NC)"
	$(CARGO) build --release --target x86_64-unknown-linux-gnu

windows: ## Build for Windows
	@echo "$(BLUE)🪟 Building for Windows...$(NC)"
	$(CARGO) build --release --target x86_64-pc-windows-gnu

macos: ## Build for macOS
	@echo "$(BLUE)🍎 Building for macOS...$(NC)"
	$(CARGO) build --release --target x86_64-apple-darwin

# Development targets
dev: ## Build development version
	@echo "$(BLUE)🔧 Building development version...$(NC)"
	$(CARGO) build

watch: ## Watch for changes and rebuild
	@echo "$(BLUE)👀 Watching for changes...$(NC)"
	$(CARGO) watch -x build

run: build ## Build and run
	@echo "$(BLUE)🏃 Running nx...$(NC)"
	./target/release/nx --version

# Documentation
docs: ## Generate documentation
	@echo "$(BLUE)📚 Generating documentation...$(NC)"
	$(CARGO) doc --no-deps --open

# Show build info
info: ## Show build information
	@echo "$(BLUE)ℹ️  Build Information$(NC)"
	@echo "$(YELLOW)Version:$(NC) $(VERSION)"
	@echo "$(YELLOW)Rust version:$(NC) $(shell rustc --version)"
	@echo "$(YELLOW)Cargo version:$(NC) $(shell cargo --version)"
	@echo "$(YELLOW)Target:$(NC) $(shell rustc -vV | grep host | cut -d' ' -f2)"
	@echo "$(YELLOW)Build dir:$(NC) $(BUILD_DIR)"
