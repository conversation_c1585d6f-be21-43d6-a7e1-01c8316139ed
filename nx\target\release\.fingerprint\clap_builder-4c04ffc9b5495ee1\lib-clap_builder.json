{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 6394549670289859781, "path": 1120524010192533516, "deps": [[5820056977320921005, "anstream", false, 9820446291527177655], [9394696648929125047, "anstyle", false, 14491688538240189572], [11166530783118767604, "strsim", false, 10459972405121130128], [11649982696571033535, "clap_lex", false, 4880041443059084995]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_builder-4c04ffc9b5495ee1\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}