# Installation Guide

This guide covers different methods to install NX Package Manager.

## Prerequisites

- Operating System: Windows, macOS, or Linux
- Node.js 16+ (for npm compatibility testing)
- Internet connection for package downloads

## Installation Methods

### Method 1: Install from npm (Recommended)

```bash
npm install -g nx-package-manager
```

After installation, the `nx` command will be available globally.

### Method 2: Download Binary Release

1. Visit the [releases page](https://github.com/nx-package-manager/nx/releases)
2. Download the appropriate binary for your platform:
   - Windows: `nx-windows-x64.exe`
   - macOS: `nx-macos-x64` or `nx-macos-arm64`
   - Linux: `nx-linux-x64`
3. Make the binary executable (macOS/Linux):
   ```bash
   chmod +x nx-*
   ```
4. Move to a directory in your PATH:
   ```bash
   sudo mv nx-* /usr/local/bin/nx
   ```

### Method 3: Build from Source

Requirements:
- Rust 1.70.0 or later
- Git

Steps:
```bash
# Clone the repository
git clone https://github.com/nx-package-manager/nx.git
cd nx

# Build the release binary
cargo build --release

# The binary will be available at target/release/nx
# Add to PATH or copy to a directory in PATH
```

## Verification

Verify the installation:

```bash
nx --version
nx --help
```

## Configuration

NX works out of the box with sensible defaults. For advanced configuration:

1. Create configuration directory:
   ```bash
   mkdir -p ~/.config/nx
   ```

2. Create configuration file `~/.config/nx/config.toml`:
   ```toml
   [network]
   timeout_seconds = 30
   max_concurrent_downloads = 50
   
   [cache]
   enabled = true
   max_size_mb = 1024
   
   [ui]
   progress_bars = true
   colors = true
   ```

## Environment Variables

Configure NX using environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `NX_TIMEOUT` | Network timeout in seconds | 30 |
| `NX_MAX_CONCURRENT` | Max concurrent downloads | 50 |
| `NX_CACHE_DIR` | Cache directory | `~/.cache/nx` |
| `NX_REGISTRY` | Registry URL | `https://registry.npmjs.org` |
| `NX_VERBOSE` | Enable verbose output | false |

Example:
```bash
export NX_VERBOSE=true
export NX_MAX_CONCURRENT=100
nx install
```

## Migration from npm

NX is designed as a drop-in replacement for npm:

1. Install NX using any method above
2. Navigate to your existing Node.js project
3. Run `nx install` instead of `npm install`
4. Update your scripts to use `nx` instead of `npm`

### Package.json Scripts

Update your package.json scripts:

```json
{
  "scripts": {
    "start": "nx run start",
    "build": "nx run build",
    "test": "nx run test",
    "install-deps": "nx install"
  }
}
```

## Troubleshooting

### Common Issues

**Command not found:**
- Ensure the binary is in your PATH
- Restart your terminal after installation

**Permission denied:**
- Make sure the binary is executable: `chmod +x nx`
- Use `sudo` if installing to system directories

**Network timeouts:**
- Increase timeout: `export NX_TIMEOUT=60`
- Check your internet connection
- Try a different registry if needed

**Cache issues:**
- Clear cache: `nx clean --all`
- Check cache directory permissions

### Getting Help

- Check the [documentation](README.md)
- Report issues on [GitHub](https://github.com/nx-package-manager/nx/issues)
- Join community discussions

## Uninstallation

### If installed via npm:
```bash
npm uninstall -g nx-package-manager
```

### If installed via binary:
```bash
rm /usr/local/bin/nx  # or wherever you installed it
rm -rf ~/.config/nx   # remove configuration
rm -rf ~/.cache/nx    # remove cache
```

### If built from source:
```bash
rm target/release/nx  # or wherever you copied it
rm -rf ~/.config/nx   # remove configuration
rm -rf ~/.cache/nx    # remove cache
```

## Next Steps

- Read the [README](README.md) for usage examples
- Check the [Contributing Guide](CONTRIBUTING.md) to contribute
- Review the [Changelog](CHANGELOG.md) for updates
