#!/bin/bash
# Production build script for nx package manager

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
VERSION=${1:-$(cargo metadata --no-deps --format-version 1 | jq -r '.packages[0].version')}
BUILD_DIR="target/release-builds"
TARGETS=(
    "x86_64-unknown-linux-gnu"
    "x86_64-unknown-linux-musl"
    "x86_64-pc-windows-gnu"
    "x86_64-apple-darwin"
    "aarch64-apple-darwin"
    "aarch64-unknown-linux-gnu"
)

echo -e "${BLUE}🚀 Building nx package manager v${VERSION} for production${NC}"
echo -e "${BLUE}================================================${NC}"

# Clean previous builds
echo -e "${YELLOW}🧹 Cleaning previous builds...${NC}"
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"

# Install required targets
echo -e "${YELLOW}🔧 Installing cross-compilation targets...${NC}"
for target in "${TARGETS[@]}"; do
    echo -e "  Installing ${target}..."
    rustup target add "$target" || echo -e "  ${YELLOW}Warning: Could not install ${target}${NC}"
done

# Install cross if not available
if ! command -v cross &> /dev/null; then
    echo -e "${YELLOW}📦 Installing cross for cross-compilation...${NC}"
    cargo install cross
fi

# Function to build for a specific target
build_target() {
    local target=$1
    local output_dir="$BUILD_DIR/$target"
    local binary_name="nx"
    
    if [[ "$target" == *"windows"* ]]; then
        binary_name="nx.exe"
    fi
    
    echo -e "${BLUE}🔨 Building for ${target}...${NC}"
    
    # Use cross for cross-compilation, cargo for native builds
    if [[ "$target" == "$(rustc -vV | sed -n 's|host: ||p')" ]]; then
        # Native build with maximum optimization
        RUSTFLAGS="-C target-cpu=native -C link-arg=-s" \
        cargo build --release --target "$target" --bin nx
    else
        # Cross-compilation
        cross build --release --target "$target" --bin nx
    fi
    
    if [ $? -eq 0 ]; then
        mkdir -p "$output_dir"
        cp "target/$target/release/$binary_name" "$output_dir/"
        
        # Strip binary if possible
        if command -v strip &> /dev/null && [[ "$target" != *"windows"* ]]; then
            strip "$output_dir/$binary_name" 2>/dev/null || true
        fi
        
        # Get binary size
        local size=$(du -h "$output_dir/$binary_name" | cut -f1)
        echo -e "${GREEN}✅ Built ${target} (${size})${NC}"
        
        # Create archive
        cd "$output_dir"
        if [[ "$target" == *"windows"* ]]; then
            zip -q "../nx-${VERSION}-${target}.zip" "$binary_name"
        else
            tar -czf "../nx-${VERSION}-${target}.tar.gz" "$binary_name"
        fi
        cd - > /dev/null
        
        echo -e "${GREEN}📦 Created archive for ${target}${NC}"
    else
        echo -e "${RED}❌ Failed to build ${target}${NC}"
        return 1
    fi
}

# Build for all targets
echo -e "${YELLOW}🏗️  Building for all targets...${NC}"
failed_targets=()

for target in "${TARGETS[@]}"; do
    if ! build_target "$target"; then
        failed_targets+=("$target")
    fi
done

# Profile-guided optimization build (if supported)
echo -e "${YELLOW}🎯 Building with Profile-Guided Optimization...${NC}"
if cargo build --profile pgo --target x86_64-unknown-linux-gnu 2>/dev/null; then
    mkdir -p "$BUILD_DIR/pgo"
    cp "target/x86_64-unknown-linux-gnu/pgo/nx" "$BUILD_DIR/pgo/"
    strip "$BUILD_DIR/pgo/nx" 2>/dev/null || true
    cd "$BUILD_DIR/pgo"
    tar -czf "../nx-${VERSION}-x86_64-linux-pgo.tar.gz" "nx"
    cd - > /dev/null
    echo -e "${GREEN}✅ Created PGO optimized build${NC}"
else
    echo -e "${YELLOW}⚠️  PGO build not available${NC}"
fi

# Generate checksums
echo -e "${YELLOW}🔐 Generating checksums...${NC}"
cd "$BUILD_DIR"
find . -name "*.tar.gz" -o -name "*.zip" | while read -r file; do
    sha256sum "$file" >> "nx-${VERSION}-checksums.txt"
done
cd - > /dev/null

# Build summary
echo -e "${BLUE}================================================${NC}"
echo -e "${GREEN}🎉 Build Summary${NC}"
echo -e "${GREEN}Version: ${VERSION}${NC}"
echo -e "${GREEN}Successful builds: $((${#TARGETS[@]} - ${#failed_targets[@]}))/${#TARGETS[@]}${NC}"

if [ ${#failed_targets[@]} -gt 0 ]; then
    echo -e "${RED}Failed targets: ${failed_targets[*]}${NC}"
fi

echo -e "${GREEN}Build artifacts available in: ${BUILD_DIR}${NC}"
echo -e "${GREEN}Checksums: ${BUILD_DIR}/nx-${VERSION}-checksums.txt${NC}"

# Performance test on native build
if [ -f "target/release/nx" ]; then
    echo -e "${YELLOW}🚀 Running quick performance test...${NC}"
    time ./target/release/nx --version > /dev/null
    echo -e "${GREEN}✅ Performance test completed${NC}"
fi

echo -e "${BLUE}🚀 Production build completed successfully!${NC}"
