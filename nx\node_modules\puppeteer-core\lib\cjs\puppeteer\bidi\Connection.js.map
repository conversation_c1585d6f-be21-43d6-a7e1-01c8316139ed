{"version": 3, "file": "Connection.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Connection.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAIH,uEAA+D;AAE/D,iDAAyC;AAEzC,+DAAuD;AACvD,+CAA6C;AAC7C,iDAAyC;AAEzC,mDAA+C;AAO/C,MAAM,iBAAiB,GAAG,IAAA,gBAAK,EAAC,gCAAgC,CAAC,CAAC;AAClE,MAAM,oBAAoB,GAAG,IAAA,gBAAK,EAAC,gCAAgC,CAAC,CAAC;AAoBrE;;GAEG;AACH,MAAa,cACX,SAAQ,8BAAwB;IAGhC,IAAI,CAAS;IACb,UAAU,CAAsB;IAChC,MAAM,CAAS;IACf,QAAQ,GAAG,CAAC,CAAC;IACb,OAAO,GAAG,KAAK,CAAC;IAChB,UAAU,GAAG,IAAI,sCAAgB,EAAE,CAAC;IACpC,SAAS,GAA6B,EAAE,CAAC;IAEzC,YACE,GAAW,EACX,SAA8B,EAC9B,KAAK,GAAG,CAAC,EACT,OAAgB;QAEhB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,OAAO,CAAC;QAEnC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,MAAM,CAA4B,OAA6B;QAC7D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAEQ,IAAI,CACX,IAAS,EACT,KAA0C;QAE1C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,IAAI,CACF,MAAS,EACT,MAA6B,EAC7B,OAAgB;QAEhB,IAAA,kBAAM,EAAC,CAAC,IAAI,CAAC,OAAO,EAAE,oCAAoC,CAAC,CAAC;QAE5D,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE;YACnE,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;gBACxC,EAAE;gBACF,MAAM;gBACN,MAAM;aACS,CAAC,CAAC;YACnB,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC,CAAiD,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,OAAe;QACvC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACpB,OAAO,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC;QACD,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,MAAM,GAA8B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9D,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;YACrB,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,SAAS;oBACZ,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;oBAC3C,OAAO;gBACT,KAAK,OAAO;oBACV,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;wBACvB,MAAM;oBACR,CAAC;oBACD,IAAI,CAAC,UAAU,CAAC,MAAM,CACpB,MAAM,CAAC,EAAE,EACT,mBAAmB,CAAC,MAAM,CAAC,EAC3B,GAAG,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,OAAO,EAAE,CACrC,CAAC;oBACF,OAAO;gBACT,KAAK,OAAO;oBACV,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;wBACvB,8BAAc,CAAC,QAAQ;6BACpB,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;4BAC3B,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;wBACpD,OAAO;oBACT,CAAC;oBACD,6DAA6D;oBAC7D,IAAI,CAAC,IAAI,CACP,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,MAAsC,CAC9C,CAAC;oBACF,OAAO;YACX,CAAC;QACH,CAAC;QACD,mFAAmF;QACnF,8EAA8E;QAC9E,IAAI,IAAI,IAAI,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CACnB,MAAuB,CAAC,EAAE,EAC3B,4DAA4D,OAAO,GAAG,EACtE,MAAM,CAAC,OAAO,CACf,CAAC;QACJ,CAAC;QACD,IAAA,oBAAU,EAAC,MAAM,CAAC,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACH,MAAM;QACJ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,+CAA+C;QAC/C,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QAEnC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED,wBAAwB;QACtB,OAAO,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE,CAAC;IACpD,CAAC;CACF;AArJD,wCAqJC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,MAA0B;IACrD,IAAI,OAAO,GAAG,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;IAClD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;QACtB,OAAO,IAAI,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;IACrC,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,UAAU,CAAC,KAA8B;IAChD,OAAO,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAC9C,CAAC"}