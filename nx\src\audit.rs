// Security audit system for production deployment

use crate::config::get_config;
use crate::errors::{NxError, NxResult};
use crate::network::NetworkClient;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SecurityAudit {
    pub timestamp: u64,
    pub total_packages: usize,
    pub vulnerabilities: Vec<Vulnerability>,
    pub summary: AuditSummary,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vulnerability {
    pub id: String,
    pub package_name: String,
    pub package_version: String,
    pub severity: Severity,
    pub title: String,
    pub description: String,
    pub recommendation: String,
    pub cwe: Option<String>,
    pub cvss_score: Option<f64>,
    pub references: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum Severity {
    Info,
    Low,
    Moderate,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditSummary {
    pub total_vulnerabilities: usize,
    pub critical: usize,
    pub high: usize,
    pub moderate: usize,
    pub low: usize,
    pub info: usize,
}

pub struct SecurityAuditor {
    network_client: NetworkClient,
    config_audit_level: Severity,
}

impl SecurityAuditor {
    pub fn new() -> Self {
        let config = get_config();
        let audit_level = match config.security.audit_level.as_str() {
            "critical" => Severity::Critical,
            "high" => Severity::High,
            "moderate" => Severity::Moderate,
            "low" => Severity::Low,
            _ => Severity::Info,
        };

        Self {
            network_client: NetworkClient::global().as_ref().clone(),
            config_audit_level: audit_level,
        }
    }

    pub async fn audit_packages(&self, packages: &[String]) -> NxResult<SecurityAudit> {
        let mut vulnerabilities = Vec::new();
        
        for package in packages {
            let package_vulns = self.check_package_vulnerabilities(package).await?;
            vulnerabilities.extend(package_vulns);
        }

        vulnerabilities.retain(|v| v.severity >= self.config_audit_level);
        let summary = self.create_summary(&vulnerabilities);

        Ok(SecurityAudit {
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            total_packages: packages.len(),
            vulnerabilities,
            summary,
        })
    }

    async fn check_package_vulnerabilities(&self, package_name: &str) -> NxResult<Vec<Vulnerability>> {
        let mut vulnerabilities = Vec::new();
        let known_vulnerable = self.get_known_vulnerable_packages();
        
        if let Some(vuln_info) = known_vulnerable.get(package_name) {
            vulnerabilities.push(Vulnerability {
                id: format!("NX-{}-001", package_name.to_uppercase()),
                package_name: package_name.to_string(),
                package_version: "unknown".to_string(),
                severity: vuln_info.severity.clone(),
                title: vuln_info.title.clone(),
                description: vuln_info.description.clone(),
                recommendation: vuln_info.recommendation.clone(),
                cwe: vuln_info.cwe.clone(),
                cvss_score: vuln_info.cvss_score,
                references: vuln_info.references.clone(),
            });
        }

        Ok(vulnerabilities)
    }

    fn get_known_vulnerable_packages(&self) -> HashMap<String, VulnerabilityInfo> {
        let mut vulns = HashMap::new();
        
        vulns.insert("lodash".to_string(), VulnerabilityInfo {
            severity: Severity::High,
            title: "Prototype Pollution".to_string(),
            description: "Lodash versions prior to 4.17.12 are vulnerable to prototype pollution".to_string(),
            recommendation: "Update to lodash version 4.17.12 or later".to_string(),
            cwe: Some("CWE-1321".to_string()),
            cvss_score: Some(7.5),
            references: vec![
                "https://github.com/advisories/GHSA-jf85-cpcp-j695".to_string(),
            ],
        });

        vulns
    }

    fn create_summary(&self, vulnerabilities: &[Vulnerability]) -> AuditSummary {
        let mut summary = AuditSummary {
            total_vulnerabilities: vulnerabilities.len(),
            critical: 0,
            high: 0,
            moderate: 0,
            low: 0,
            info: 0,
        };

        for vuln in vulnerabilities {
            match vuln.severity {
                Severity::Critical => summary.critical += 1,
                Severity::High => summary.high += 1,
                Severity::Moderate => summary.moderate += 1,
                Severity::Low => summary.low += 1,
                Severity::Info => summary.info += 1,
            }
        }

        summary
    }
}

#[derive(Debug, Clone)]
struct VulnerabilityInfo {
    severity: Severity,
    title: String,
    description: String,
    recommendation: String,
    cwe: Option<String>,
    cvss_score: Option<f64>,
    references: Vec<String>,
}

pub struct IntegrityVerifier;

impl IntegrityVerifier {
    pub fn verify_package_integrity(package_path: &Path, expected_checksum: Option<&str>) -> NxResult<bool> {
        if !package_path.exists() {
            return Err(NxError::io(format!("Package file not found: {}", package_path.display())));
        }

        if let Some(expected) = expected_checksum {
            let actual_checksum = Self::calculate_checksum(package_path)?;
            Ok(actual_checksum == expected)
        } else {
            Ok(package_path.is_file())
        }
    }

    fn calculate_checksum(file_path: &Path) -> NxResult<String> {
        use std::io::Read;
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut file = std::fs::File::open(file_path)
            .map_err(|e| NxError::io(format!("Failed to open file: {}", e)))?;
        
        let mut hasher = DefaultHasher::new();
        let mut buffer = [0; 8192];
        
        loop {
            let bytes_read = file.read(&mut buffer)
                .map_err(|e| NxError::io(format!("Failed to read file: {}", e)))?;
            
            if bytes_read == 0 {
                break;
            }
            
            buffer[..bytes_read].hash(&mut hasher);
        }

        Ok(format!("{:x}", hasher.finish()))
    }
}

pub struct TrustedRegistryManager {
    trusted_registries: Vec<String>,
}

impl TrustedRegistryManager {
    pub fn new() -> Self {
        let config = get_config();
        Self {
            trusted_registries: config.security.trusted_registries.clone(),
        }
    }

    pub fn is_registry_trusted(&self, registry_url: &str) -> bool {
        self.trusted_registries.iter().any(|trusted| registry_url.starts_with(trusted))
    }
}
