#!/bin/bash
# Universal installation script for nx package manager

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPO_URL="https://github.com/your-org/nx"
RELEASES_URL="$REPO_URL/releases"
INSTALL_DIR="${INSTALL_DIR:-/usr/local/bin}"
BINARY_NAME="nx"
VERSION="${VERSION:-latest}"

# System detection
OS=$(uname -s | tr '[:upper:]' '[:lower:]')
ARCH=$(uname -m)

# Map architecture names
case $ARCH in
    x86_64) ARCH="x86_64" ;;
    amd64) ARCH="x86_64" ;;
    arm64) ARCH="aarch64" ;;
    aarch64) ARCH="aarch64" ;;
    *) 
        echo -e "${RED}❌ Unsupported architecture: $ARCH${NC}"
        exit 1
        ;;
esac

# Map OS names and determine file extension
case $OS in
    linux)
        if ldd --version 2>&1 | grep -q musl; then
            TARGET="$ARCH-unknown-linux-musl"
        else
            TARGET="$ARCH-unknown-linux-gnu"
        fi
        FILE_EXT="tar.gz"
        ;;
    darwin)
        TARGET="$ARCH-apple-darwin"
        FILE_EXT="tar.gz"
        ;;
    mingw*|msys*|cygwin*)
        TARGET="$ARCH-pc-windows-gnu"
        FILE_EXT="zip"
        BINARY_NAME="nx.exe"
        ;;
    *)
        echo -e "${RED}❌ Unsupported operating system: $OS${NC}"
        exit 1
        ;;
esac

echo -e "${BLUE}🚀 NX Package Manager Installer${NC}"
echo -e "${BLUE}===============================${NC}"
echo -e "${YELLOW}OS: $OS${NC}"
echo -e "${YELLOW}Architecture: $ARCH${NC}"
echo -e "${YELLOW}Target: $TARGET${NC}"
echo -e "${YELLOW}Install Directory: $INSTALL_DIR${NC}"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
echo -e "${YELLOW}🔍 Checking dependencies...${NC}"
if ! command_exists curl && ! command_exists wget; then
    echo -e "${RED}❌ Neither curl nor wget found. Please install one of them.${NC}"
    exit 1
fi

if ! command_exists tar && [ "$FILE_EXT" = "tar.gz" ]; then
    echo -e "${RED}❌ tar not found. Please install tar.${NC}"
    exit 1
fi

if ! command_exists unzip && [ "$FILE_EXT" = "zip" ]; then
    echo -e "${RED}❌ unzip not found. Please install unzip.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Dependencies satisfied${NC}"

# Function to download file
download_file() {
    local url="$1"
    local output="$2"
    
    if command_exists curl; then
        curl -fsSL "$url" -o "$output"
    elif command_exists wget; then
        wget -q "$url" -O "$output"
    else
        echo -e "${RED}❌ No download tool available${NC}"
        return 1
    fi
}

# Get latest version if not specified
if [ "$VERSION" = "latest" ]; then
    echo -e "${YELLOW}🔍 Fetching latest version...${NC}"
    
    if command_exists curl; then
        VERSION=$(curl -fsSL "$RELEASES_URL/latest" | grep -o '"tag_name": *"[^"]*"' | cut -d'"' -f4)
    elif command_exists wget; then
        VERSION=$(wget -qO- "$RELEASES_URL/latest" | grep -o '"tag_name": *"[^"]*"' | cut -d'"' -f4)
    fi
    
    if [ -z "$VERSION" ]; then
        echo -e "${RED}❌ Could not determine latest version${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Latest version: $VERSION${NC}"
fi

# Construct download URL
FILENAME="nx-$VERSION-$TARGET.$FILE_EXT"
DOWNLOAD_URL="$RELEASES_URL/download/$VERSION/$FILENAME"

echo -e "${YELLOW}📦 Downloading nx $VERSION for $TARGET...${NC}"
echo -e "   URL: $DOWNLOAD_URL"

# Create temporary directory
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# Download the archive
if ! download_file "$DOWNLOAD_URL" "$TEMP_DIR/$FILENAME"; then
    echo -e "${RED}❌ Failed to download $FILENAME${NC}"
    echo -e "${YELLOW}💡 Available releases: $RELEASES_URL${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Downloaded successfully${NC}"

# Extract the archive
echo -e "${YELLOW}📂 Extracting archive...${NC}"
cd "$TEMP_DIR"

if [ "$FILE_EXT" = "tar.gz" ]; then
    tar -xzf "$FILENAME"
elif [ "$FILE_EXT" = "zip" ]; then
    unzip -q "$FILENAME"
fi

# Verify binary exists
if [ ! -f "$BINARY_NAME" ]; then
    echo -e "${RED}❌ Binary not found in archive${NC}"
    exit 1
fi

# Make binary executable
chmod +x "$BINARY_NAME"

# Test the binary
echo -e "${YELLOW}🧪 Testing binary...${NC}"
if ! ./"$BINARY_NAME" --version >/dev/null 2>&1; then
    echo -e "${RED}❌ Binary test failed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Binary test passed${NC}"

# Install the binary
echo -e "${YELLOW}📦 Installing to $INSTALL_DIR...${NC}"

# Check if we need sudo
if [ ! -w "$INSTALL_DIR" ]; then
    if command_exists sudo; then
        echo -e "${YELLOW}🔐 Requesting sudo privileges for installation...${NC}"
        sudo cp "$BINARY_NAME" "$INSTALL_DIR/"
        sudo chmod +x "$INSTALL_DIR/$BINARY_NAME"
    else
        echo -e "${RED}❌ No write permission to $INSTALL_DIR and sudo not available${NC}"
        echo -e "${YELLOW}💡 Try running with: INSTALL_DIR=\$HOME/.local/bin $0${NC}"
        exit 1
    fi
else
    cp "$BINARY_NAME" "$INSTALL_DIR/"
    chmod +x "$INSTALL_DIR/$BINARY_NAME"
fi

echo -e "${GREEN}✅ Installation completed successfully!${NC}"

# Verify installation
echo -e "${YELLOW}🔍 Verifying installation...${NC}"
if command_exists nx; then
    INSTALLED_VERSION=$(nx --version 2>/dev/null | head -1 || echo "unknown")
    echo -e "${GREEN}✅ nx is available in PATH${NC}"
    echo -e "${GREEN}   Version: $INSTALLED_VERSION${NC}"
else
    echo -e "${YELLOW}⚠️  nx not found in PATH${NC}"
    echo -e "${YELLOW}💡 You may need to add $INSTALL_DIR to your PATH${NC}"
    echo -e "${YELLOW}   Add this to your shell profile:${NC}"
    echo -e "${YELLOW}   export PATH=\"$INSTALL_DIR:\$PATH\"${NC}"
fi

# Show next steps
echo ""
echo -e "${BLUE}🎉 Installation Complete!${NC}"
echo -e "${BLUE}========================${NC}"
echo ""
echo -e "${GREEN}Next steps:${NC}"
echo -e "1. Verify installation: ${YELLOW}nx --version${NC}"
echo -e "2. Get help: ${YELLOW}nx --help${NC}"
echo -e "3. Install packages: ${YELLOW}nx install lodash${NC}"
echo -e "4. Check performance: ${YELLOW}time nx install react --dry-run${NC}"
echo ""
echo -e "${GREEN}Documentation: ${BLUE}$REPO_URL#readme${NC}"
echo -e "${GREEN}Report issues: ${BLUE}$REPO_URL/issues${NC}"
echo ""
echo -e "${BLUE}🚀 Happy package managing with nx!${NC}"
