{"rustc": 1842507548689473721, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 3592778941406178886, "path": 2592930079643880870, "deps": [[41016358116313498, "hyper_util", false, 12936134084200506558], [784494742817713399, "tower_service", false, 4788937879189487147], [2883436298747778685, "pki_types", false, 6249722070552777526], [8153991275959898788, "webpki_roots", false, 8149879891668776693], [9010263965687315507, "http", false, 1892032584777174270], [11895591994124935963, "tokio_rustls", false, 18011413126716263812], [11957360342995674422, "hyper", false, 12178671363772744532], [12393800526703971956, "tokio", false, 4223665201178837825], [14619257664405537057, "rustls", false, 12358600061817176247]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hyper-rustls-8f90ade1d44ecb38\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}