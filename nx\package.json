{"name": "nx-package-manager", "version": "1.0.0", "description": "A fast, reliable Node.js package manager built with Rust", "main": "bin/nx", "bin": {"nx": "./bin/nx"}, "scripts": {"build": "cargo build --release", "build:debug": "cargo build", "test": "cargo test", "test:integration": "cargo test --test integration_tests", "test:stress": "cargo test --test stress_tests", "bench": "cargo bench", "clean": "cargo clean", "install:local": "cargo build --release && npm link", "lint": "cargo clippy -- -D warnings", "format": "cargo fmt", "docs": "cargo doc --open", "coverage": "cargo tarpaulin --out html", "validate": "npm run lint && npm run test && npm run bench"}, "dependencies": {}, "devDependencies": {}, "keywords": ["package-manager", "npm", "node", "rust", "fast", "cli", "dependency-management", "javascript", "nodejs"], "author": {"name": "NX Package Manager Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nx-package-manager/nx.git"}, "bugs": {"url": "https://github.com/nx-package-manager/nx/issues"}, "homepage": "https://github.com/nx-package-manager/nx#readme", "engines": {"node": ">=16.0.0"}, "os": ["darwin", "linux", "win32"], "cpu": ["x64", "arm64"], "files": ["bin/", "README.md", "LICENSE", "CHANGELOG.md"]}