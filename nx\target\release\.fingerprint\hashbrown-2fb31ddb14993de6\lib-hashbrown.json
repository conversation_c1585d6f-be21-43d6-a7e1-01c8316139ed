{"rustc": 1842507548689473721, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 15625788057656754760, "path": 7746456528949997686, "deps": [[5230392855116717286, "equivalent", false, 7749856218339755893], [9150530836556604396, "allocator_api2", false, 5169972126231175168], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 6719560670936567396]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-2fb31ddb14993de6\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}