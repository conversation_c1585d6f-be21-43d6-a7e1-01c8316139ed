{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15625788057656754760, "path": 15822400793770354601, "deps": [[555019317135488525, "regex_automata", false, 153052840138990007], [9408802513701742484, "regex_syntax", false, 3636772361951488735]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-56b8f9b0ab8a4b12\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}