// Progress module with indicatif-based UI

use indicatif::{MultiProgress, ProgressBar as IndicatifProgressBar, ProgressStyle, ProgressState, ProgressFinish};
use std::fmt::Write;
use std::sync::Arc;
use std::time::{Duration, Instant};
use once_cell::sync::Lazy;

// Global multi-progress instance for coordinated display
static MULTI_PROGRESS: Lazy<Arc<MultiProgress>> = Lazy::new(|| {
    Arc::new(MultiProgress::new())
});

// Custom progress bar wrapper with enhanced features
pub struct ProgressBar {
    bar: IndicatifProgressBar,
    start_time: Instant,
    package_name: String,
}

impl ProgressBar {
    pub fn new(total: usize, package_name: &str) -> Self {
        let bar = MULTI_PROGRESS.add(IndicatifProgressBar::new(total as u64));

        // Progress bar style with colors and indicators
        let style = ProgressStyle::with_template(
            "📦 {spinner:.cyan} {msg:<20} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {bytes}/{total_bytes} ({bytes_per_sec}, {eta})"
        )
        .unwrap()
        .with_key("eta", |state: &ProgressState, w: &mut dyn Write| {
            write!(w, "{:.1}s", state.eta().as_secs_f64()).unwrap()
        })
        .progress_chars("█▉▊▋▌▍▎▏  ");

        bar.set_style(style);
        bar.set_message(package_name.to_string());
        bar.enable_steady_tick(Duration::from_millis(100));

        ProgressBar {
            bar,
            start_time: Instant::now(),
            package_name: package_name.to_string(),
        }
    }

    pub fn update(&mut self, current: usize) {
        self.bar.set_position(current as u64);
    }

    pub fn increment(&mut self) {
        self.bar.inc(1);
    }

    pub fn finish(&mut self) {
        let elapsed = self.start_time.elapsed();
        self.bar.finish_with_message(format!("✅ {} ({:.2}s)", self.package_name, elapsed.as_secs_f64()));
    }

    pub fn finish_with_error(&mut self, error: &str) {
        self.bar.finish_with_message(format!("❌ {} - {}", self.package_name, error));
    }
}

// Spinner for long-running operations
pub struct Spinner {
    bar: IndicatifProgressBar,
}

impl Spinner {
    pub fn new(message: &str) -> Self {
        let bar = MULTI_PROGRESS.add(IndicatifProgressBar::new_spinner());

        let style = ProgressStyle::with_template(
            "{spinner:.cyan} {msg}"
        )
        .unwrap()
        .tick_strings(&[
            "⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"
        ]);

        bar.set_style(style);
        bar.set_message(message.to_string());
        bar.enable_steady_tick(Duration::from_millis(80));

        Spinner { bar }
    }

    pub fn set_message(&self, message: &str) {
        self.bar.set_message(message.to_string());
    }

    pub fn finish_with_message(&self, message: &str) {
        self.bar.finish_with_message(message.to_string());
    }

    pub fn finish_and_clear(&self) {
        self.bar.finish_and_clear();
    }
}

// Enhanced message functions with better styling
pub fn show_success(message: &str) {
    println!("✅ \x1b[1;32m{}\x1b[0m", message);
}

pub fn show_error(message: &str) {
    eprintln!("❌ \x1b[1;31m{}\x1b[0m", message);
}

pub fn show_warning(message: &str) {
    println!("⚠️  \x1b[1;33m{}\x1b[0m", message);
}

pub fn show_info(message: &str) {
    println!("ℹ️  \x1b[1;36m{}\x1b[0m", message);
}

pub fn show_step(step: usize, total: usize, message: &str) {
    println!("📋 \x1b[1;35m[{}/{}]\x1b[0m {}", step, total, message);
}

// Installation summary with formatting
pub fn show_install_summary(packages: &[String], total_time: Duration) {
    println!("\n🎉 \x1b[1;32mInstallation Complete!\x1b[0m");
    println!("┌─────────────────────────────────────────────────────────────┐");
    println!("│ 📦 Installed \x1b[1;36m{}\x1b[0m package{} in \x1b[1;33m{:.2}s\x1b[0m{}",
        packages.len(),
        if packages.len() == 1 { "" } else { "s" },
        total_time.as_secs_f64(),
        " ".repeat(20_usize.saturating_sub(packages.len().to_string().len() + total_time.as_secs_f64().to_string().len()))
    );

    if packages.len() <= 8 {
        println!("├─────────────────────────────────────────────────────────────┤");
        for (i, package) in packages.iter().enumerate() {
            let icon = if i == packages.len() - 1 { "└─" } else { "├─" };
            println!("│ {} ✅ \x1b[32m{}\x1b[0m", icon, package);
        }
    } else {
        println!("├─────────────────────────────────────────────────────────────┤");
        for package in &packages[..5] {
            println!("│ ├─ ✅ \x1b[32m{}\x1b[0m", package);
        }
        println!("│ └─ ... and \x1b[1;36m{}\x1b[0m more packages", packages.len() - 5);
    }

    println!("└─────────────────────────────────────────────────────────────┘");

    // Performance metrics
    let packages_per_second = packages.len() as f64 / total_time.as_secs_f64();
    println!("⚡ Performance: \x1b[1;35m{:.1} packages/second\x1b[0m", packages_per_second);
}

// Multi-progress manager for concurrent operations
pub struct MultiProgressManager {
    multi: Arc<MultiProgress>,
}

impl MultiProgressManager {
    pub fn new() -> Self {
        MultiProgressManager {
            multi: MULTI_PROGRESS.clone(),
        }
    }

    pub fn create_progress_bar(&self, total: u64, message: &str) -> IndicatifProgressBar {
        let pb = self.multi.add(IndicatifProgressBar::new(total));

        let style = ProgressStyle::with_template(
            "🔄 {spinner:.green} {msg:<25} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {pos:>7}/{len:7} {eta}"
        )
        .unwrap()
        .progress_chars("█▉▊▋▌▍▎▏  ");

        pb.set_style(style);
        pb.set_message(message.to_string());
        pb.enable_steady_tick(Duration::from_millis(100));
        pb
    }

    pub fn create_spinner(&self, message: &str) -> IndicatifProgressBar {
        let spinner = self.multi.add(IndicatifProgressBar::new_spinner());

        let style = ProgressStyle::with_template(
            "{spinner:.cyan} {msg}"
        )
        .unwrap()
        .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]);

        spinner.set_style(style);
        spinner.set_message(message.to_string());
        spinner.enable_steady_tick(Duration::from_millis(80));
        spinner
    }
}

// Utility function for formatting bytes
pub fn format_bytes(bytes: usize) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

// Global access functions
pub fn get_multi_progress() -> Arc<MultiProgress> {
    MULTI_PROGRESS.clone()
}
