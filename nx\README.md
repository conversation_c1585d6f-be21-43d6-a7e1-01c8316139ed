# NX Package Manager

A fast, reliable Node.js package manager built with Rust, designed as a drop-in replacement for npm with improved performance and modern features.

[![Build Status](https://img.shields.io/github/workflow/status/nx-package-manager/nx/CI)](https://github.com/nx-package-manager/nx/actions)
[![Rust](https://img.shields.io/badge/Built_with-Rust-orange)](https://www.rust-lang.org/)
[![License](https://img.shields.io/badge/License-MIT-blue)](LICENSE)
[![npm version](https://img.shields.io/npm/v/nx-package-manager)](https://www.npmjs.com/package/nx-package-manager)

## Features

- **Performance**: Optimized package installation and dependency resolution
- **HTTP/2 Support**: Modern networking with connection pooling and multiplexing
- **Intelligent Caching**: Multi-level caching system with automatic cleanup
- **Parallel Processing**: Concurrent downloads and installations
- **Memory Efficient**: Streaming downloads and optimized memory usage
- **npm Compatible**: Drop-in replacement for npm with familiar commands

## Installation

### From npm

```bash
npm install -g nx-package-manager
```

### From Source

```bash
git clone https://github.com/nx-package-manager/nx.git
cd nx
cargo build --release
```

### Binary Releases

Download pre-built binaries from the [releases page](https://github.com/nx-package-manager/nx/releases).

## Quick Start

NX is designed to be a drop-in replacement for npm. Most npm commands work identically:

```bash
# Install dependencies from package.json
nx install

# Install specific packages
nx install lodash axios react

# Install development dependencies
nx install --save-dev typescript @types/node

# Update packages
nx update

# Run scripts
nx run build
nx run test
nx start

# Search for packages
nx search react

# Get package information
nx info lodash
```

## Performance

NX provides improved performance over npm through several optimizations:

- **Parallel Downloads**: Concurrent package downloads with configurable limits
- **HTTP/2 Support**: Modern networking protocols for faster transfers
- **Intelligent Caching**: Multi-level caching reduces redundant operations
- **Optimized Dependency Resolution**: Faster algorithm for resolving package dependencies

Performance improvements vary based on project size, network conditions, and system specifications.

## Commands

### Package Management

```bash
nx install [packages...]     # Install packages
nx uninstall <packages...>   # Remove packages
nx update [packages...]      # Update packages
nx outdated                  # Check for outdated packages
nx audit [--fix]            # Security audit
```

### Script Execution

```bash
nx run <script>             # Run package.json script
nx start                    # Run start script
nx test                     # Run test script
nx build                    # Run build script
nx dev                      # Run dev script
```

### Information & Utilities

```bash
nx info <package>           # Show package information
nx search <query>           # Search for packages
nx clean [--all]           # Clean cache
nx --version               # Show version
```

### Workspace Management

```bash
nx workspace list          # List workspaces
nx workspace run <script>  # Run script in all workspaces
```

## Configuration

NX can be configured via `~/.config/nx/config.toml` or environment variables.

### Configuration File

Create `~/.config/nx/config.toml`:

```toml
[network]
timeout_seconds = 30
max_concurrent_downloads = 50
max_retries = 3

[cache]
enabled = true
max_size_mb = 1024
directory = "~/.cache/nx"

[registry]
url = "https://registry.npmjs.org"
strict_ssl = true

[ui]
progress_bars = true
colors = true
verbose = false
```

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NX_TIMEOUT` | Network timeout in seconds | 30 |
| `NX_MAX_CONCURRENT` | Maximum concurrent downloads | 50 |
| `NX_CACHE_DIR` | Cache directory path | `~/.cache/nx` |
| `NX_REGISTRY` | Registry URL | `https://registry.npmjs.org` |
| `NX_NO_PROGRESS` | Disable progress bars | false |
| `NX_VERBOSE` | Enable verbose output | false |

## Architecture

NX is built with a modular architecture designed for performance and maintainability:

### Core Components

- **Network Layer** (`network.rs`) - HTTP/2 client with connection pooling and retry logic
- **Cache System** (`cache.rs`) - Multi-level caching with TTL and disk persistence
- **Dependency Resolver** (`resolver.rs`) - Package version resolution and dependency tree building
- **Progress System** (`progress.rs`) - Terminal UI components and progress tracking
- **Package Manager** (`installer.rs`) - Core package installation and management logic

## Development

### Building from Source

```bash
git clone https://github.com/nx-package-manager/nx.git
cd nx
cargo build --release
```

### Running Tests

```bash
# Run all tests
cargo test

# Run integration tests
cargo test --test integration_tests

# Run with coverage
cargo tarpaulin --out html
```

### Benchmarking

```bash
# Run performance benchmarks
cargo bench

# Generate benchmark reports
cargo bench -- --output-format html
```

## Contributing

We welcome contributions! Please read our [Contributing Guide](CONTRIBUTING.md) for details on:

- Development setup
- Code style guidelines
- Testing requirements
- Pull request process

### Development Setup

1. Install Rust (1.70.0 or later)
2. Clone the repository
3. Run `cargo build` to compile
4. Run `cargo test` to verify installation

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

Built with these excellent open source projects:

- [Rust](https://www.rust-lang.org/) - Systems programming language
- [Tokio](https://tokio.rs/) - Asynchronous runtime
- [Reqwest](https://github.com/seanmonstar/reqwest) - HTTP client
- [Indicatif](https://github.com/console-rs/indicatif) - Progress bars
- [Clap](https://github.com/clap-rs/clap) - Command line parsing
