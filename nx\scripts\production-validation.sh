#!/bin/bash
# Comprehensive production validation script for nx package manager

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
NX_BINARY="${1:-./target/release/nx}"
VALIDATION_DIR="production-validation"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$VALIDATION_DIR/production_validation_$TIMESTAMP.md"

echo -e "${BLUE}🚀 NX Package Manager Production Validation${NC}"
echo -e "${BLUE}=============================================${NC}"

# Ensure binary exists
if [ ! -f "$NX_BINARY" ]; then
    echo -e "${RED}❌ Binary not found: $NX_BINARY${NC}"
    echo -e "${YELLOW}💡 Run 'make build' or 'cargo build --release' first${NC}"
    exit 1
fi

# Create validation directory
mkdir -p "$VALIDATION_DIR"

# Initialize report
cat > "$REPORT_FILE" << EOF
# NX Package Manager Production Validation Report

**Generated:** $(date)
**Binary:** $NX_BINARY
**System:** $(uname -a)
**Validation ID:** $TIMESTAMP

## Executive Summary

This report validates the production readiness of the NX package manager, including performance, security, reliability, and ecosystem compatibility.

## Validation Results

EOF

echo -e "${YELLOW}📋 Starting comprehensive production validation...${NC}"

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
WARNING_TESTS=0

# Function to run validation test
run_validation_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    local test_type="${4:-CRITICAL}"
    
    echo -e "${BLUE}🔬 Testing: $test_name${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    local start_time=$(date +%s.%N)
    local test_output
    local test_result
    
    if test_output=$(eval "$test_command" 2>&1); then
        test_result="PASS"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        echo -e "${GREEN}✅ PASS: $test_name${NC}"
    else
        if [ "$test_type" = "WARNING" ]; then
            test_result="WARNING"
            WARNING_TESTS=$((WARNING_TESTS + 1))
            echo -e "${YELLOW}⚠️  WARNING: $test_name${NC}"
        else
            test_result="FAIL"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            echo -e "${RED}❌ FAIL: $test_name${NC}"
        fi
    fi
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    
    # Add to report
    cat >> "$REPORT_FILE" << EOF
### $test_name

**Type:** $test_type
**Result:** $test_result
**Duration:** ${duration}s
**Command:** \`$test_command\`
**Expected:** $expected_result

\`\`\`
$test_output
\`\`\`

EOF
    
    echo ""
}

# 1. Basic Functionality Tests
echo -e "${YELLOW}🔧 Basic Functionality Tests${NC}"

run_validation_test \
    "Binary Execution" \
    "$NX_BINARY --version" \
    "Should display version information" \
    "CRITICAL"

run_validation_test \
    "Help Command" \
    "$NX_BINARY --help" \
    "Should display help information" \
    "CRITICAL"

run_validation_test \
    "Configuration Loading" \
    "$NX_BINARY info lodash --dry-run" \
    "Should load configuration without errors" \
    "CRITICAL"

# 2. Performance Tests
echo -e "${YELLOW}⚡ Performance Tests${NC}"

run_validation_test \
    "Fast Package Info Fetch" \
    "timeout 10s $NX_BINARY info lodash" \
    "Should complete within 10 seconds" \
    "CRITICAL"

run_validation_test \
    "Dependency Resolution Speed" \
    "timeout 30s $NX_BINARY install react express --dry-run" \
    "Should resolve dependencies within 30 seconds" \
    "CRITICAL"

run_validation_test \
    "Cache Performance" \
    "$NX_BINARY info express && $NX_BINARY info express" \
    "Second request should be faster (cached)" \
    "WARNING"

# 3. Memory and Resource Tests
echo -e "${YELLOW}🧠 Memory and Resource Tests${NC}"

if command -v valgrind >/dev/null 2>&1; then
    run_validation_test \
        "Memory Leak Detection" \
        "valgrind --leak-check=full --error-exitcode=1 $NX_BINARY --version" \
        "Should not have memory leaks" \
        "WARNING"
else
    echo -e "${YELLOW}⚠️  Valgrind not available, skipping memory leak test${NC}"
fi

run_validation_test \
    "Resource Usage" \
    "/usr/bin/time -v $NX_BINARY info lodash 2>&1 | grep 'Maximum resident set size'" \
    "Should use reasonable memory" \
    "WARNING"

# 4. Security Tests
echo -e "${YELLOW}🔒 Security Tests${NC}"

run_validation_test \
    "Security Audit Command" \
    "$NX_BINARY audit --help" \
    "Should provide audit functionality" \
    "CRITICAL"

run_validation_test \
    "Configuration Security" \
    "test -f ~/.config/nx/config.toml || echo 'No config file found'" \
    "Should handle missing config gracefully" \
    "WARNING"

# 5. Error Handling Tests
echo -e "${YELLOW}🚨 Error Handling Tests${NC}"

run_validation_test \
    "Invalid Package Handling" \
    "$NX_BINARY info non-existent-package-12345 || true" \
    "Should handle non-existent packages gracefully" \
    "CRITICAL"

run_validation_test \
    "Network Error Handling" \
    "timeout 5s $NX_BINARY info some-package || true" \
    "Should handle network timeouts gracefully" \
    "CRITICAL"

run_validation_test \
    "Invalid Command Handling" \
    "$NX_BINARY invalid-command || true" \
    "Should handle invalid commands gracefully" \
    "CRITICAL"

# 6. Ecosystem Compatibility Tests
echo -e "${YELLOW}🌐 Ecosystem Compatibility Tests${NC}"

# Create temporary test directory
TEST_DIR=$(mktemp -d)
cd "$TEST_DIR"

# Create test package.json
cat > package.json << EOF
{
  "name": "nx-test-project",
  "version": "1.0.0",
  "dependencies": {
    "lodash": "^4.17.21"
  }
}
EOF

run_validation_test \
    "Package.json Compatibility" \
    "$NX_BINARY install --dry-run" \
    "Should read package.json correctly" \
    "CRITICAL"

run_validation_test \
    "NPM Workspace Detection" \
    "ls package.json && echo 'Package.json found'" \
    "Should detect npm project structure" \
    "WARNING"

# Cleanup
cd - > /dev/null
rm -rf "$TEST_DIR"

# 7. Stress Tests
echo -e "${YELLOW}💪 Stress Tests${NC}"

run_validation_test \
    "Concurrent Operations" \
    "for i in {1..5}; do $NX_BINARY info lodash & done; wait" \
    "Should handle concurrent operations" \
    "WARNING"

run_validation_test \
    "Large Dependency Tree" \
    "timeout 60s $NX_BINARY install @angular/core --dry-run" \
    "Should handle large dependency trees" \
    "WARNING"

# 8. Signal Handling Tests
echo -e "${YELLOW}📡 Signal Handling Tests${NC}"

run_validation_test \
    "Graceful Shutdown" \
    "timeout 5s $NX_BINARY info lodash || echo 'Timeout handled'" \
    "Should handle interruption gracefully" \
    "WARNING"

# 9. Cross-Platform Tests
echo -e "${YELLOW}🌍 Cross-Platform Tests${NC}"

run_validation_test \
    "Path Handling" \
    "$NX_BINARY info lodash --dry-run" \
    "Should handle file paths correctly" \
    "CRITICAL"

run_validation_test \
    "Unicode Support" \
    "echo 'Testing unicode: 🚀 📦 ⚡' && $NX_BINARY --version" \
    "Should handle unicode characters" \
    "WARNING"

# 10. Production Readiness Tests
echo -e "${YELLOW}🏭 Production Readiness Tests${NC}"

run_validation_test \
    "Binary Size Check" \
    "ls -lh $NX_BINARY | awk '{print \$5}' | grep -E '^[0-9]+[KM]?B?\$'" \
    "Binary should be reasonably sized" \
    "WARNING"

run_validation_test \
    "Startup Time" \
    "time $NX_BINARY --version" \
    "Should start quickly" \
    "WARNING"

run_validation_test \
    "Clean Exit" \
    "$NX_BINARY --version; echo \"Exit code: \$?\"" \
    "Should exit cleanly" \
    "CRITICAL"

# Generate final report
echo -e "${BLUE}📊 Generating final validation report...${NC}"

cat >> "$REPORT_FILE" << EOF

## Validation Summary

### Test Results
- **Total Tests:** $TOTAL_TESTS
- **Passed:** $PASSED_TESTS
- **Failed:** $FAILED_TESTS
- **Warnings:** $WARNING_TESTS

### Success Rate
- **Critical Tests:** $(echo "scale=1; ($PASSED_TESTS - $WARNING_TESTS) * 100 / ($TOTAL_TESTS - $WARNING_TESTS)" | bc -l)%
- **Overall Tests:** $(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l)%

### Production Readiness Assessment

EOF

# Determine production readiness
if [ $FAILED_TESTS -eq 0 ]; then
    if [ $WARNING_TESTS -le 3 ]; then
        READINESS="✅ PRODUCTION READY"
        READINESS_COLOR=$GREEN
    else
        READINESS="⚠️  PRODUCTION READY WITH WARNINGS"
        READINESS_COLOR=$YELLOW
    fi
else
    READINESS="❌ NOT PRODUCTION READY"
    READINESS_COLOR=$RED
fi

cat >> "$REPORT_FILE" << EOF
**Status:** $READINESS

### Recommendations

EOF

if [ $FAILED_TESTS -gt 0 ]; then
    cat >> "$REPORT_FILE" << EOF
- **Critical Issues:** $FAILED_TESTS critical test(s) failed. These must be resolved before production deployment.
EOF
fi

if [ $WARNING_TESTS -gt 3 ]; then
    cat >> "$REPORT_FILE" << EOF
- **Performance Optimization:** Consider addressing warning-level issues for optimal performance.
EOF
fi

cat >> "$REPORT_FILE" << EOF
- **Monitoring:** Implement production monitoring and alerting.
- **Backup Strategy:** Ensure proper backup and recovery procedures.
- **Documentation:** Verify all operational documentation is complete.

### Next Steps

1. Review and address any failed tests
2. Consider optimizations for warning-level issues
3. Conduct user acceptance testing
4. Prepare production deployment plan
5. Set up monitoring and alerting

---

**Validation completed at:** $(date)
**Report generated by:** NX Production Validation Suite
EOF

# Final summary
echo -e "${BLUE}================================================${NC}"
echo -e "${READINESS_COLOR}$READINESS${NC}"
echo -e "${BLUE}================================================${NC}"
echo -e "${GREEN}📊 Test Results:${NC}"
echo -e "   Total Tests: $TOTAL_TESTS"
echo -e "   Passed: $PASSED_TESTS"
echo -e "   Failed: $FAILED_TESTS"
echo -e "   Warnings: $WARNING_TESTS"
echo -e "${GREEN}📄 Full report: $REPORT_FILE${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 NX Package Manager is ready for production deployment!${NC}"
    exit 0
else
    echo -e "${RED}🚨 Production deployment blocked by $FAILED_TESTS critical issue(s)${NC}"
    exit 1
fi
