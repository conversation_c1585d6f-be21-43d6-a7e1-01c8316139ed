{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 249948210049039588, "path": 6335723327130817083, "deps": [[2883436298747778685, "pki_types", false, 5464178065119522018], [3722963349756955755, "once_cell", false, 12245328027315973588], [5491919304041016563, "ring", false, 12913665814940204352], [6528079939221783635, "zeroize", false, 7539871369158397859], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 5627993640658140087], [14619257664405537057, "build_script_build", false, 4282812327993418898], [17003143334332120809, "subtle", false, 6082335043739735431]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-6d42a1d149791bf8\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}