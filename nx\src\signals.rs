// Signal handling and graceful shutdown for production deployment

use crate::telemetry::{flush_telemetry, get_telemetry};
use crate::cache::{get_package_cache, get_download_cache};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::signal;
use once_cell::sync::Lazy;

// Global shutdown flag
static SHUTDOWN_REQUESTED: Lazy<Arc<AtomicBool>> = Lazy::new(|| {
    Arc::new(AtomicBool::new(false))
});

// Global cleanup handlers
static CLEANUP_HANDLERS: Lazy<Arc<tokio::sync::Mutex<Vec<Box<dyn Fn() + Send + Sync>>>>> = Lazy::new(|| {
    Arc::new(tokio::sync::Mutex::new(Vec::new()))
});

pub struct SignalHandler {
    shutdown_flag: Arc<AtomicBool>,
}

impl SignalHandler {
    pub fn new() -> Self {
        Self {
            shutdown_flag: SHUTDOWN_REQUESTED.clone(),
        }
    }

    pub async fn setup_signal_handlers(&self) {
        let shutdown_flag = self.shutdown_flag.clone();
        
        tokio::spawn(async move {
            #[cfg(unix)]
            {
                let mut sigterm = signal::unix::signal(signal::unix::SignalKind::terminate())
                    .expect("Failed to register SIGTERM handler");
                let mut sigint = signal::unix::signal(signal::unix::SignalKind::interrupt())
                    .expect("Failed to register SIGINT handler");
                let mut sigquit = signal::unix::signal(signal::unix::SignalKind::quit())
                    .expect("Failed to register SIGQUIT handler");

                tokio::select! {
                    _ = sigterm.recv() => {
                        println!("\n🛑 Received SIGTERM, initiating graceful shutdown...");
                        shutdown_flag.store(true, Ordering::SeqCst);
                        graceful_shutdown().await;
                    }
                    _ = sigint.recv() => {
                        println!("\n🛑 Received SIGINT (Ctrl+C), initiating graceful shutdown...");
                        shutdown_flag.store(true, Ordering::SeqCst);
                        graceful_shutdown().await;
                    }
                    _ = sigquit.recv() => {
                        println!("\n🛑 Received SIGQUIT, initiating graceful shutdown...");
                        shutdown_flag.store(true, Ordering::SeqCst);
                        graceful_shutdown().await;
                    }
                }
            }

            #[cfg(windows)]
            {
                let mut ctrl_c = signal::windows::ctrl_c()
                    .expect("Failed to register Ctrl+C handler");
                let mut ctrl_break = signal::windows::ctrl_break()
                    .expect("Failed to register Ctrl+Break handler");
                let mut ctrl_close = signal::windows::ctrl_close()
                    .expect("Failed to register Ctrl+Close handler");

                tokio::select! {
                    _ = ctrl_c.recv() => {
                        println!("\n🛑 Received Ctrl+C, initiating graceful shutdown...");
                        shutdown_flag.store(true, Ordering::SeqCst);
                        graceful_shutdown().await;
                    }
                    _ = ctrl_break.recv() => {
                        println!("\n🛑 Received Ctrl+Break, initiating graceful shutdown...");
                        shutdown_flag.store(true, Ordering::SeqCst);
                        graceful_shutdown().await;
                    }
                    _ = ctrl_close.recv() => {
                        println!("\n🛑 Received Ctrl+Close, initiating graceful shutdown...");
                        shutdown_flag.store(true, Ordering::SeqCst);
                        graceful_shutdown().await;
                    }
                }
            }
        });
    }

    pub fn is_shutdown_requested(&self) -> bool {
        self.shutdown_flag.load(Ordering::SeqCst)
    }

    pub async fn wait_for_shutdown(&self) {
        while !self.is_shutdown_requested() {
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }
    }
}

async fn graceful_shutdown() {
    println!("🔄 Starting graceful shutdown sequence...");

    // Step 1: Stop accepting new operations
    println!("   ⏹️  Stopping new operations...");
    
    // Step 2: Wait for ongoing operations to complete (with timeout)
    println!("   ⏳ Waiting for ongoing operations to complete...");
    let timeout = tokio::time::Duration::from_secs(30);
    let start = tokio::time::Instant::now();
    
    while start.elapsed() < timeout {
        // In a real implementation, you'd check for ongoing operations
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        
        // For now, just wait a short time
        if start.elapsed() > tokio::time::Duration::from_secs(2) {
            break;
        }
    }

    // Step 3: Flush caches
    println!("   💾 Flushing caches...");
    let package_cache = get_package_cache();
    package_cache.clear_expired().await;
    
    let download_cache = get_download_cache();
    download_cache.clear_expired().await;

    // Step 4: Flush telemetry
    println!("   📊 Flushing telemetry data...");
    if let Err(e) = flush_telemetry().await {
        eprintln!("   ⚠️  Failed to flush telemetry: {}", e);
    }

    // Step 5: Run custom cleanup handlers
    println!("   🧹 Running cleanup handlers...");
    let handlers = CLEANUP_HANDLERS.lock().await;
    for handler in handlers.iter() {
        handler();
    }

    // Step 6: Generate session summary
    println!("   📋 Generating session summary...");
    let summary = get_telemetry().get_session_summary().await;
    println!("   📊 Session Summary:");
    println!("      - Total Events: {}", summary.total_events);
    println!("      - Successful Operations: {}", summary.successful_events);
    println!("      - Error Count: {}", summary.error_events);
    println!("      - Packages Processed: {}", summary.total_packages_processed);
    println!("      - Cache Hit Rate: {:.1}%", summary.cache_hit_rate);

    println!("✅ Graceful shutdown completed");
    std::process::exit(0);
}

pub fn register_cleanup_handler<F>(handler: F)
where
    F: Fn() + Send + Sync + 'static,
{
    tokio::spawn(async move {
        let mut handlers = CLEANUP_HANDLERS.lock().await;
        handlers.push(Box::new(handler));
    });
}

pub fn is_shutdown_requested() -> bool {
    SHUTDOWN_REQUESTED.load(Ordering::SeqCst)
}

// Rollback mechanism for failed installations
pub struct RollbackManager {
    operations: Vec<RollbackOperation>,
}

#[derive(Debug, Clone)]
pub enum RollbackOperation {
    RemoveFile { path: std::path::PathBuf },
    RemoveDirectory { path: std::path::PathBuf },
    RestoreFile { original_path: std::path::PathBuf, backup_path: std::path::PathBuf },
    UpdatePackageJson { backup_content: String },
}

impl RollbackManager {
    pub fn new() -> Self {
        Self {
            operations: Vec::new(),
        }
    }

    pub fn add_operation(&mut self, operation: RollbackOperation) {
        self.operations.push(operation);
    }

    pub async fn execute_rollback(&self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🔄 Executing rollback operations...");
        
        for (i, operation) in self.operations.iter().rev().enumerate() {
            println!("   {}/{} Rolling back: {:?}", i + 1, self.operations.len(), operation);
            
            match operation {
                RollbackOperation::RemoveFile { path } => {
                    if path.exists() {
                        std::fs::remove_file(path)?;
                    }
                }
                RollbackOperation::RemoveDirectory { path } => {
                    if path.exists() {
                        std::fs::remove_dir_all(path)?;
                    }
                }
                RollbackOperation::RestoreFile { original_path, backup_path } => {
                    if backup_path.exists() {
                        std::fs::copy(backup_path, original_path)?;
                        std::fs::remove_file(backup_path)?;
                    }
                }
                RollbackOperation::UpdatePackageJson { backup_content } => {
                    std::fs::write("package.json", backup_content)?;
                }
            }
        }
        
        println!("✅ Rollback completed successfully");
        Ok(())
    }

    pub fn clear(&mut self) {
        self.operations.clear();
    }
}

// Production logging system
pub struct ProductionLogger {
    log_level: LogLevel,
    log_file: Option<std::path::PathBuf>,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum LogLevel {
    Error = 0,
    Warn = 1,
    Info = 2,
    Debug = 3,
    Trace = 4,
}

impl ProductionLogger {
    pub fn new(log_level: LogLevel, log_file: Option<std::path::PathBuf>) -> Self {
        Self { log_level, log_file }
    }

    pub fn log(&self, level: LogLevel, message: &str) {
        if level <= self.log_level {
            let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f UTC");
            let level_str = match level {
                LogLevel::Error => "ERROR",
                LogLevel::Warn => "WARN ",
                LogLevel::Info => "INFO ",
                LogLevel::Debug => "DEBUG",
                LogLevel::Trace => "TRACE",
            };

            let log_entry = format!("[{}] {} {}", timestamp, level_str, message);

            // Print to console
            match level {
                LogLevel::Error => eprintln!("{}", log_entry),
                _ => println!("{}", log_entry),
            }

            // Write to log file if configured
            if let Some(ref log_file) = self.log_file {
                if let Ok(mut file) = std::fs::OpenOptions::new()
                    .create(true)
                    .append(true)
                    .open(log_file)
                {
                    use std::io::Write;
                    let _ = writeln!(file, "{}", log_entry);
                }
            }
        }
    }

    pub fn error(&self, message: &str) {
        self.log(LogLevel::Error, message);
    }

    pub fn warn(&self, message: &str) {
        self.log(LogLevel::Warn, message);
    }

    pub fn info(&self, message: &str) {
        self.log(LogLevel::Info, message);
    }

    pub fn debug(&self, message: &str) {
        self.log(LogLevel::Debug, message);
    }

    pub fn trace(&self, message: &str) {
        self.log(LogLevel::Trace, message);
    }
}

// Ecosystem compatibility checker
pub struct EcosystemCompatibility;

impl EcosystemCompatibility {
    pub fn check_npm_compatibility() -> CompatibilityReport {
        let mut issues = Vec::new();
        let mut warnings = Vec::new();

        // Check for package.json
        if !std::path::Path::new("package.json").exists() {
            warnings.push("No package.json found - some npm features may not work".to_string());
        }

        // Check for node_modules
        if std::path::Path::new("node_modules").exists() {
            warnings.push("Existing node_modules directory found - consider cleaning before using nx".to_string());
        }

        // Check for package-lock.json
        if std::path::Path::new("package-lock.json").exists() {
            warnings.push("package-lock.json found - nx uses its own dependency resolution".to_string());
        }

        // Check Node.js version
        if let Ok(output) = std::process::Command::new("node").arg("--version").output() {
            let version = String::from_utf8_lossy(&output.stdout);
            if !version.starts_with("v") {
                issues.push("Node.js not found or invalid version".to_string());
            }
        } else {
            warnings.push("Node.js not found - some features may not work".to_string());
        }

        CompatibilityReport {
            compatible: issues.is_empty(),
            issues,
            warnings,
        }
    }
}

#[derive(Debug)]
pub struct CompatibilityReport {
    pub compatible: bool,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
}

// Global signal handler setup
pub async fn setup_production_signal_handling() {
    let handler = SignalHandler::new();
    handler.setup_signal_handlers().await;
}
