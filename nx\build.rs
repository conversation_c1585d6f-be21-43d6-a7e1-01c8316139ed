// Build script for NX Package Manager
// Handles build-time configuration and optimization

use std::env;
use std::fs;
use std::path::Path;

fn main() {
    // Set build timestamp
    println!("cargo:rustc-env=BUILD_TIMESTAMP={}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));
    
    // Set git commit hash if available
    if let Ok(output) = std::process::Command::new("git")
        .args(&["rev-parse", "--short", "HEAD"])
        .output()
    {
        if output.status.success() {
            let git_hash = String::from_utf8_lossy(&output.stdout).trim().to_string();
            println!("cargo:rustc-env=GIT_HASH={}", git_hash);
        }
    }
    
    // Enable optimizations for release builds
    if env::var("PROFILE").unwrap_or_default() == "release" {
        // Link-time optimization
        println!("cargo:rustc-link-arg=-flto");
        
        // Strip debug symbols
        println!("cargo:rustc-link-arg=-s");
    }
    
    // Create bin directory if it doesn't exist
    let bin_dir = Path::new("bin");
    if !bin_dir.exists() {
        fs::create_dir_all(bin_dir).expect("Failed to create bin directory");
    }
    
    // Copy binary to bin directory after build
    println!("cargo:rerun-if-changed=src/");
    println!("cargo:rerun-if-changed=Cargo.toml");
    println!("cargo:rerun-if-changed=Cargo.lock");
}
