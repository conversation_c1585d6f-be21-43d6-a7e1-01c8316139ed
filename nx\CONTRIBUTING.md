# Contributing to NX Package Manager

Thank you for your interest in contributing to NX! This document provides guidelines and information for contributors.

## Development Setup

### Prerequisites

- Rust 1.70.0 or later
- Git
- Node.js 16+ (for testing npm compatibility)

### Getting Started

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/your-username/nx.git
   cd nx
   ```

3. Build the project:
   ```bash
   cargo build
   ```

4. Run tests to verify your setup:
   ```bash
   cargo test
   ```

## Development Workflow

### Making Changes

1. Create a new branch for your feature or fix:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes following our coding standards
3. Add tests for new functionality
4. Ensure all tests pass:
   ```bash
   cargo test
   cargo test --test integration_tests
   ```

5. Run the linter:
   ```bash
   cargo clippy -- -D warnings
   ```

6. Format your code:
   ```bash
   cargo fmt
   ```

### Testing

We maintain several types of tests:

- **Unit Tests**: Test individual functions and modules
- **Integration Tests**: Test complete workflows
- **Stress Tests**: Test performance under load
- **Benchmarks**: Measure performance improvements

Run specific test suites:
```bash
# Unit tests
cargo test --lib

# Integration tests
cargo test --test integration_tests

# Stress tests
cargo test --test stress_tests

# Benchmarks
cargo bench
```

### Code Style

- Follow Rust standard formatting (`cargo fmt`)
- Use meaningful variable and function names
- Add documentation comments for public APIs
- Keep functions focused and small
- Handle errors appropriately

### Commit Messages

Use clear, descriptive commit messages:

```
feat: add parallel package installation
fix: resolve dependency resolution bug
docs: update installation instructions
test: add integration tests for search command
```

## Project Structure

```
nx/
├── src/
│   ├── main.rs          # Application entry point
│   ├── cli.rs           # Command-line interface
│   ├── installer.rs     # Package installation logic
│   ├── network.rs       # HTTP client and networking
│   ├── cache.rs         # Caching system
│   ├── resolver.rs      # Dependency resolution
│   ├── progress.rs      # Progress indicators
│   └── ...
├── tests/               # Integration tests
├── benches/            # Performance benchmarks
└── scripts/            # Build and utility scripts
```

## Adding New Features

### Before Starting

1. Check existing issues and discussions
2. Create an issue to discuss major changes
3. Ensure the feature aligns with project goals

### Implementation Guidelines

1. **Performance**: Maintain or improve performance
2. **Compatibility**: Ensure npm compatibility where applicable
3. **Error Handling**: Provide clear, actionable error messages
4. **Documentation**: Update relevant documentation
5. **Tests**: Add comprehensive tests

### Example: Adding a New Command

1. Add command definition in `cli.rs`
2. Implement command logic in appropriate module
3. Add error handling and validation
4. Write unit and integration tests
5. Update documentation

## Performance Considerations

NX prioritizes performance. When contributing:

- Profile your changes with `cargo bench`
- Avoid unnecessary allocations
- Use efficient data structures
- Consider async/await patterns for I/O
- Test with large dependency trees

## Documentation

Update documentation for:

- New commands or options
- Changed behavior
- Performance improvements
- Breaking changes

Documentation locations:
- `README.md` - Main project documentation
- `CHANGELOG.md` - Version history
- Code comments - API documentation
- `docs/` - Detailed guides (if applicable)

## Submitting Changes

### Pull Request Process

1. Ensure your branch is up to date with main:
   ```bash
   git fetch origin
   git rebase origin/main
   ```

2. Push your changes:
   ```bash
   git push origin feature/your-feature-name
   ```

3. Create a pull request on GitHub

4. Fill out the pull request template completely

5. Respond to review feedback promptly

### Pull Request Requirements

- [ ] All tests pass
- [ ] Code follows style guidelines
- [ ] Documentation is updated
- [ ] Performance impact is considered
- [ ] Breaking changes are documented

## Release Process

Releases are managed by maintainers:

1. Version bumping follows semantic versioning
2. Changelog is updated with notable changes
3. Performance benchmarks are run
4. Release notes are prepared

## Getting Help

- **Issues**: Report bugs or request features
- **Discussions**: Ask questions or discuss ideas
- **Documentation**: Check existing docs first
- **Code Review**: Maintainers will review PRs

## Code of Conduct

We are committed to providing a welcoming and inclusive environment. Please:

- Be respectful and constructive
- Focus on the technical aspects
- Help others learn and grow
- Report inappropriate behavior

## Recognition

Contributors are recognized in:
- Release notes
- Contributor list
- Special recognition for significant contributions

Thank you for contributing to NX Package Manager!
