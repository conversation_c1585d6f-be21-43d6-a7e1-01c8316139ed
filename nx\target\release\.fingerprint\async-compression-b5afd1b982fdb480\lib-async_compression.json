{"rustc": 1842507548689473721, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 15625788057656754760, "path": 5623080562496243947, "deps": [[1906322745568073236, "pin_project_lite", false, 10947738494670116878], [7620660491849607393, "futures_core", false, 11645144072932315121], [9556762810601084293, "brotli", false, 7769074562652263656], [12393800526703971956, "tokio", false, 15112758162455251723], [15932120279885307830, "memchr", false, 2223923019234557440], [17772299992546037086, "flate2", false, 17999086496185362138]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\async-compression-b5afd1b982fdb480\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}