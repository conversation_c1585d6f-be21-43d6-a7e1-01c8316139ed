{"version": 3, "file": "HTTPRequest.js", "sourceRoot": "", "sources": ["../../../../src/cdp/HTTPRequest.ts"], "names": [], "mappings": ";;;AASA,0DAQ+B;AAC/B,+CAA6C;AAC7C,qDAAmD;AAInD;;GAEG;AACH,MAAa,cAAe,SAAQ,4BAAW;IACpC,EAAE,CAAS;IAIpB,OAAO,CAAa;IACpB,oBAAoB,CAAU;IAE9B,IAAI,CAAS;IACb,aAAa,CAAe;IAE5B,OAAO,CAAS;IAChB,YAAY,GAAG,KAAK,CAAC;IACrB,SAAS,CAAU;IACnB,QAAQ,GAA2B,EAAE,CAAC;IACtC,MAAM,CAAe;IACrB,UAAU,CAA8B;IAExC,IAAa,MAAM;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAa,MAAM,CAAC,SAAqB;QACvC,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;IAC3B,CAAC;IAED,YACE,MAAkB,EAClB,KAAmB,EACnB,cAAkC,EAClC,iBAA0B,EAC1B,IAyBC,EACD,aAA+B;QAE/B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QACzB,IAAI,CAAC,oBAAoB;YACvB,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,WAAW,EAAkB,CAAC;QAC1E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,iBAAiB,CAAC;QAE9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC;QAC3C,CAAC;IACH,CAAC;IAEQ,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEQ,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEQ,KAAK,CAAC,aAAa;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACnE,SAAS,EAAE,IAAI,CAAC,EAAE;aACnB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC,QAAQ,CAAC;QACzB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAA,oBAAU,EAAC,GAAG,CAAC,CAAC;YAChB,OAAO;QACT,CAAC;IACH,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEQ,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEQ,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAEQ,OAAO;QACd,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,YAAY;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,YAAsC,EAAE;QACtD,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAC,GAAG,SAAS,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QAEjC,MAAM,oBAAoB,GAAG,QAAQ;YACnC,CAAC,CAAC,IAAA,4BAAc,EAAC,QAAQ,CAAC;YAC1B,CAAC,CAAC,SAAS,CAAC;QAEd,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,yEAAyE,CAC1E,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,CAAC,OAAO;aACf,IAAI,CAAC,uBAAuB,EAAE;YAC7B,SAAS,EAAE,IAAI,CAAC,eAAe;YAC/B,GAAG;YACH,MAAM;YACN,QAAQ,EAAE,oBAAoB;YAC9B,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAA,6BAAY,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;SACrD,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC;YAClC,OAAO,IAAA,4BAAW,EAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAqC;QAClD,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QAEjC,IAAI,UAKS,CAAC;QACd,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClB,UAAU,GAAG,4BAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,eAAe,GAAsC,EAAE,CAAC;QAC9D,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAEvC,eAAe,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;oBAC1D,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACf,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;oBACtB,CAAC,CAAC;oBACJ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QACD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,eAAe,CAAC,cAAc,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzD,CAAC;QACD,IAAI,UAAU,EAAE,aAAa,IAAI,CAAC,CAAC,gBAAgB,IAAI,eAAe,CAAC,EAAE,CAAC;YACxE,eAAe,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC;QACtC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,wEAAwE,CACzE,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,CAAC,OAAO;aACf,IAAI,CAAC,sBAAsB,EAAE;YAC5B,SAAS,EAAE,IAAI,CAAC,eAAe;YAC/B,YAAY,EAAE,MAAM;YACpB,cAAc,EAAE,6BAAY,CAAC,MAAM,CAAC;YACpC,eAAe,EAAE,IAAA,6BAAY,EAAC,eAAe,CAAC;YAC9C,IAAI,EAAE,UAAU,EAAE,MAAM;SACzB,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC;YAClC,OAAO,IAAA,4BAAW,EAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,MAAM,CACV,WAAgD;QAEhD,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QACjC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,qEAAqE,CACtE,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,CAAC,OAAO;aACf,IAAI,CAAC,mBAAmB,EAAE;YACzB,SAAS,EAAE,IAAI,CAAC,eAAe;YAC/B,WAAW,EAAE,WAAW,IAAI,QAAQ;SACrC,CAAC;aACD,KAAK,CAAC,4BAAW,CAAC,CAAC;IACxB,CAAC;CACF;AApPD,wCAoPC"}