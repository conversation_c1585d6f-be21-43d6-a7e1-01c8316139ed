{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"brotli\", \"gzip\", \"h2\", \"http2\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 10616629412618305977, "path": 11523913256638171667, "deps": [[40386456601120721, "percent_encoding", false, 15396888382059259172], [41016358116313498, "hyper_util", false, 12749235721146326749], [784494742817713399, "tower_service", false, 5636654777102118069], [1288403060204016458, "tokio_util", false, 16012044231610530129], [1788832197870803419, "hyper_rustls", false, 9703934245273953030], [1906322745568073236, "pin_project_lite", false, 10947738494670116878], [2054153378684941554, "tower_http", false, 15985078233413769850], [2517136641825875337, "sync_wrapper", false, 3528646051741126886], [2883436298747778685, "rustls_pki_types", false, 5464178065119522018], [3150220818285335163, "url", false, 1858783516641417601], [5695049318159433696, "tower", false, 3980642350994473926], [5986029879202738730, "log", false, 13748057073333940098], [7620660491849607393, "futures_core", false, 11645144072932315121], [8153991275959898788, "webpki_roots", false, 1832230504372623973], [8569119365930580996, "serde_json", false, 12887773269120189484], [9010263965687315507, "http", false, 5926620808744162099], [9689903380558560274, "serde", false, 9689167156444042997], [10629569228670356391, "futures_util", false, 15824389852012147554], [11895591994124935963, "tokio_rustls", false, 12341422077213980510], [11957360342995674422, "hyper", false, 13611362547814066969], [12393800526703971956, "tokio", false, 15112758162455251723], [13077212702700853852, "base64", false, 1028263715188468606], [14084095096285906100, "http_body", false, 2496751000400218397], [14359893265615549706, "h2", false, 18116248593665842463], [14619257664405537057, "rustls", false, 774807013914914820], [14721851354164625169, "async_compression", false, 9420111202940612682], [16066129441945555748, "bytes", false, 6765633902541680566], [16542808166767769916, "serde_urlencoded", false, 14835582612481057968], [16900715236047033623, "http_body_util", false, 5618721334433601680]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-a4fe7bf85c913bc3\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}