{"rustc": 1842507548689473721, "features": "[\"alloc-stdlib\", \"default\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 8433163163091947982, "profile": 15625788057656754760, "path": 12403356785398612932, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 13846640817770895509], [16413620717702030930, "brotli_decompressor", false, 4842288588809720764], [17470296833448545982, "alloc_stdlib", false, 7112253385066946991]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\brotli-ca598baa12cc2221\\dep-lib-brotli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}