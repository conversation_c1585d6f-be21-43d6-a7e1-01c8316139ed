{"rustc": 1842507548689473721, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 15625788057656754760, "path": 2592930079643880870, "deps": [[41016358116313498, "hyper_util", false, 12749235721146326749], [784494742817713399, "tower_service", false, 5636654777102118069], [2883436298747778685, "pki_types", false, 5464178065119522018], [8153991275959898788, "webpki_roots", false, 1832230504372623973], [9010263965687315507, "http", false, 5926620808744162099], [11895591994124935963, "tokio_rustls", false, 12341422077213980510], [11957360342995674422, "hyper", false, 13611362547814066969], [12393800526703971956, "tokio", false, 15112758162455251723], [14619257664405537057, "rustls", false, 774807013914914820]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hyper-rustls-f285f576c4423e23\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}