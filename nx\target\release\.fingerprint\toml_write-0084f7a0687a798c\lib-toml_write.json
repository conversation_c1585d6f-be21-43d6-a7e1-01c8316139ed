{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 15943748010645046320, "profile": 4713345837951840373, "path": 17606788221630308709, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\toml_write-0084f7a0687a798c\\dep-lib-toml_write", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}