# Universal installation script for nx package manager (Windows)

param(
    [string]$Version = "latest",
    [string]$InstallDir = "$env:LOCALAPPDATA\nx\bin"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Configuration
$RepoUrl = "https://github.com/your-org/nx"
$ReleasesUrl = "$RepoUrl/releases"
$BinaryName = "nx.exe"

# System detection
$OS = "windows"
$Arch = $env:PROCESSOR_ARCHITECTURE.ToLower()

# Map architecture names
switch ($Arch) {
    "amd64" { $Arch = "x86_64" }
    "x86" { $Arch = "i686" }
    "arm64" { $Arch = "aarch64" }
    default {
        Write-Host "❌ Unsupported architecture: $Arch" -ForegroundColor $Red
        exit 1
    }
}

$Target = "$Arch-pc-windows-msvc"

Write-Host "🚀 NX Package Manager Installer" -ForegroundColor $Blue
Write-Host "===============================" -ForegroundColor $Blue
Write-Host "OS: $OS" -ForegroundColor $Yellow
Write-Host "Architecture: $Arch" -ForegroundColor $Yellow
Write-Host "Target: $Target" -ForegroundColor $Yellow
Write-Host "Install Directory: $InstallDir" -ForegroundColor $Yellow
Write-Host ""

# Check PowerShell version
if ($PSVersionTable.PSVersion.Major -lt 3) {
    Write-Host "❌ PowerShell 3.0 or later is required" -ForegroundColor $Red
    exit 1
}

Write-Host "✅ PowerShell version check passed" -ForegroundColor $Green

# Get latest version if not specified
if ($Version -eq "latest") {
    Write-Host "🔍 Fetching latest version..." -ForegroundColor $Yellow
    
    try {
        $LatestRelease = Invoke-RestMethod -Uri "$ReleasesUrl/latest" -UseBasicParsing
        $Version = $LatestRelease.tag_name
        Write-Host "✅ Latest version: $Version" -ForegroundColor $Green
    }
    catch {
        Write-Host "❌ Could not determine latest version: $_" -ForegroundColor $Red
        exit 1
    }
}

# Construct download URL
$Filename = "nx-$Version-$Target.zip"
$DownloadUrl = "$ReleasesUrl/download/$Version/$Filename"

Write-Host "📦 Downloading nx $Version for $Target..." -ForegroundColor $Yellow
Write-Host "   URL: $DownloadUrl" -ForegroundColor $Yellow

# Create temporary directory
$TempDir = New-TemporaryFile | ForEach-Object { Remove-Item $_; New-Item -ItemType Directory -Path $_ }

try {
    # Download the archive
    $ZipPath = Join-Path $TempDir $Filename
    
    try {
        Invoke-WebRequest -Uri $DownloadUrl -OutFile $ZipPath -UseBasicParsing
        Write-Host "✅ Downloaded successfully" -ForegroundColor $Green
    }
    catch {
        Write-Host "❌ Failed to download $Filename" -ForegroundColor $Red
        Write-Host "💡 Available releases: $ReleasesUrl" -ForegroundColor $Yellow
        exit 1
    }

    # Extract the archive
    Write-Host "📂 Extracting archive..." -ForegroundColor $Yellow
    
    try {
        Expand-Archive -Path $ZipPath -DestinationPath $TempDir -Force
        $BinaryPath = Join-Path $TempDir $BinaryName
        
        if (-not (Test-Path $BinaryPath)) {
            Write-Host "❌ Binary not found in archive" -ForegroundColor $Red
            exit 1
        }
        
        Write-Host "✅ Archive extracted successfully" -ForegroundColor $Green
    }
    catch {
        Write-Host "❌ Failed to extract archive: $_" -ForegroundColor $Red
        exit 1
    }

    # Test the binary
    Write-Host "🧪 Testing binary..." -ForegroundColor $Yellow
    
    try {
        $TestOutput = & $BinaryPath --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Binary test passed" -ForegroundColor $Green
        }
        else {
            Write-Host "❌ Binary test failed" -ForegroundColor $Red
            exit 1
        }
    }
    catch {
        Write-Host "❌ Binary test failed: $_" -ForegroundColor $Red
        exit 1
    }

    # Create install directory
    Write-Host "📦 Installing to $InstallDir..." -ForegroundColor $Yellow
    
    if (-not (Test-Path $InstallDir)) {
        try {
            New-Item -ItemType Directory -Path $InstallDir -Force | Out-Null
            Write-Host "✅ Created install directory" -ForegroundColor $Green
        }
        catch {
            Write-Host "❌ Failed to create install directory: $_" -ForegroundColor $Red
            exit 1
        }
    }

    # Install the binary
    $InstallPath = Join-Path $InstallDir $BinaryName
    
    try {
        Copy-Item $BinaryPath $InstallPath -Force
        Write-Host "✅ Binary installed successfully" -ForegroundColor $Green
    }
    catch {
        Write-Host "❌ Failed to install binary: $_" -ForegroundColor $Red
        exit 1
    }

    # Add to PATH if not already there
    $CurrentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($CurrentPath -notlike "*$InstallDir*") {
        Write-Host "🔧 Adding to PATH..." -ForegroundColor $Yellow
        
        try {
            $NewPath = "$CurrentPath;$InstallDir"
            [Environment]::SetEnvironmentVariable("PATH", $NewPath, "User")
            Write-Host "✅ Added to PATH (restart shell to take effect)" -ForegroundColor $Green
        }
        catch {
            Write-Host "⚠️  Could not add to PATH automatically" -ForegroundColor $Yellow
            Write-Host "💡 Manually add $InstallDir to your PATH" -ForegroundColor $Yellow
        }
    }

    # Verify installation
    Write-Host "🔍 Verifying installation..." -ForegroundColor $Yellow
    
    # Refresh PATH for current session
    $env:PATH = "$env:PATH;$InstallDir"
    
    try {
        $InstalledVersion = & nx --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ nx is available" -ForegroundColor $Green
            Write-Host "   Version: $InstalledVersion" -ForegroundColor $Green
        }
        else {
            Write-Host "⚠️  nx not found in PATH" -ForegroundColor $Yellow
            Write-Host "💡 You may need to restart your shell" -ForegroundColor $Yellow
        }
    }
    catch {
        Write-Host "⚠️  Could not verify installation" -ForegroundColor $Yellow
    }

    # Show next steps
    Write-Host ""
    Write-Host "🎉 Installation Complete!" -ForegroundColor $Blue
    Write-Host "========================" -ForegroundColor $Blue
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor $Green
    Write-Host "1. Restart your PowerShell/Command Prompt" -ForegroundColor $Yellow
    Write-Host "2. Verify installation: nx --version" -ForegroundColor $Yellow
    Write-Host "3. Get help: nx --help" -ForegroundColor $Yellow
    Write-Host "4. Install packages: nx install lodash" -ForegroundColor $Yellow
    Write-Host "5. Check performance: Measure-Command { nx install react --dry-run }" -ForegroundColor $Yellow
    Write-Host ""
    Write-Host "Documentation: $RepoUrl#readme" -ForegroundColor $Blue
    Write-Host "Report issues: $RepoUrl/issues" -ForegroundColor $Blue
    Write-Host ""
    Write-Host "🚀 Happy package managing with nx!" -ForegroundColor $Blue

}
finally {
    # Cleanup
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force -ErrorAction SilentlyContinue
    }
}
