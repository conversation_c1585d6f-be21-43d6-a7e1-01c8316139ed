{"rustc": 1842507548689473721, "features": "[\"client\", \"default\", \"http1\", \"http2\"]", "declared_features": "[\"capi\", \"client\", \"default\", \"ffi\", \"full\", \"http1\", \"http2\", \"nightly\", \"server\", \"tracing\"]", "target": 9574292076208557625, "profile": 6072913209463591880, "path": 6962948519091010022, "deps": [[1569313478171189446, "want", false, 8073153498025710509], [1811549171721445101, "futures_channel", false, 856346984384687233], [1906322745568073236, "pin_project_lite", false, 8023542118904931378], [3666196340704888985, "smallvec", false, 16213804259116255603], [6163892036024256188, "httparse", false, 2613142591842906493], [7695812897323945497, "itoa", false, 11018132071678058892], [9010263965687315507, "http", false, 1892032584777174270], [10629569228670356391, "futures_util", false, 17717833475852592514], [12393800526703971956, "tokio", false, 4223665201178837825], [14084095096285906100, "http_body", false, 17240980880396472347], [14359893265615549706, "h2", false, 1026034119023946496], [16066129441945555748, "bytes", false, 6440951242105093202]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hyper-32aad835924f2fcb\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}