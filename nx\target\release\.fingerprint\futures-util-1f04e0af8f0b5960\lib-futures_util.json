{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 10133871767810704183, "path": 5955513359176574756, "deps": [[1615478164327904835, "pin_utils", false, 7399882283457548174], [1906322745568073236, "pin_project_lite", false, 10947738494670116878], [5451793922601807560, "slab", false, 11804530617941630837], [7620660491849607393, "futures_core", false, 11645144072932315121], [10565019901765856648, "futures_macro", false, 4278777622593950206], [16240732885093539806, "futures_task", false, 12887426124044823970]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-1f04e0af8f0b5960\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}