{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15625788057656754760, "path": 13770176725210014611, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-561d29f11aa00af6\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}