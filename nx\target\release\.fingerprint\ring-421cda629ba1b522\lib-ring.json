{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 15625788057656754760, "path": 16034447377844208515, "deps": [[2828590642173593838, "cfg_if", false, 817368216369650493], [5491919304041016563, "build_script_build", false, 3856100441525742101], [8995469080876806959, "untrusted", false, 11545851768301826155], [9920160576179037441, "getrandom", false, 8534170770573976940]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ring-421cda629ba1b522\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}