{"rustc": 1842507548689473721, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15625788057656754760, "path": 6767152828495364124, "deps": [[7312356825837975969, "crc32fast", false, 14039520302029403645], [7636735136738807108, "miniz_oxide", false, 15262675780932380666]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\flate2-76a7abca49cba307\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}