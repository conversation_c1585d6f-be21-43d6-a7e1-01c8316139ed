// Configuration management for nx package manager

use crate::errors::{NxError, NxResult};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::fs;
use once_cell::sync::Lazy;

// Global configuration instance
static CONFIG: Lazy<NxConfig> = Lazy::new(|| {
    NxConfig::load().unwrap_or_default()
});

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NxConfig {
    // Network settings
    pub network: NetworkConfig,
    
    // Cache settings
    pub cache: CacheConfig,
    
    // Performance settings
    pub performance: PerformanceConfig,
    
    // Registry settings
    pub registry: RegistryConfig,
    
    // UI settings
    pub ui: UiConfig,
    
    // Security settings
    pub security: SecurityConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub timeout_seconds: u64,
    pub connect_timeout_seconds: u64,
    pub max_retries: u32,
    pub retry_delay_ms: u64,
    pub max_concurrent_downloads: usize,
    pub max_concurrent_per_host: usize,
    pub user_agent: String,
    pub proxy: Option<String>,
    pub no_proxy: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    pub enabled: bool,
    pub directory: PathBuf,
    pub max_size_mb: u64,
    pub package_metadata_ttl_seconds: u64,
    pub download_cache_ttl_seconds: u64,
    pub dependency_resolution_ttl_seconds: u64,
    pub cleanup_interval_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub max_parallel_extractions: usize,
    pub extraction_buffer_size: usize,
    pub memory_mapped_threshold_mb: u64,
    pub batch_size: usize,
    pub worker_threads: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryConfig {
    pub url: String,
    pub auth_token: Option<String>,
    pub always_auth: bool,
    pub ca_file: Option<PathBuf>,
    pub cert_file: Option<PathBuf>,
    pub key_file: Option<PathBuf>,
    pub strict_ssl: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiConfig {
    pub progress_bars: bool,
    pub colors: bool,
    pub unicode: bool,
    pub verbose: bool,
    pub quiet: bool,
    pub log_level: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub verify_checksums: bool,
    pub allow_unsigned_packages: bool,
    pub trusted_registries: Vec<String>,
    pub audit_level: String, // "none", "low", "moderate", "high", "critical"
}

impl Default for NxConfig {
    fn default() -> Self {
        Self {
            network: NetworkConfig {
                timeout_seconds: 30,
                connect_timeout_seconds: 10,
                max_retries: 3,
                retry_delay_ms: 1000,
                max_concurrent_downloads: 100,
                max_concurrent_per_host: 20,
                user_agent: format!("nx-package-manager/{}", env!("CARGO_PKG_VERSION")),
                proxy: std::env::var("HTTP_PROXY").ok().or_else(|| std::env::var("http_proxy").ok()),
                no_proxy: std::env::var("NO_PROXY")
                    .or_else(|_| std::env::var("no_proxy"))
                    .map(|s| s.split(',').map(|s| s.trim().to_string()).collect())
                    .unwrap_or_default(),
            },
            cache: CacheConfig {
                enabled: true,
                directory: get_default_cache_dir(),
                max_size_mb: 1024, // 1GB
                package_metadata_ttl_seconds: 1800, // 30 minutes
                download_cache_ttl_seconds: 86400, // 24 hours
                dependency_resolution_ttl_seconds: 3600, // 1 hour
                cleanup_interval_seconds: 1800, // 30 minutes
            },
            performance: PerformanceConfig {
                max_parallel_extractions: num_cpus::get(),
                extraction_buffer_size: 64 * 1024, // 64KB
                memory_mapped_threshold_mb: 5, // 5MB
                batch_size: 50,
                worker_threads: None, // Auto-detect
            },
            registry: RegistryConfig {
                url: "https://registry.npmjs.org".to_string(),
                auth_token: std::env::var("NPM_TOKEN").ok(),
                always_auth: false,
                ca_file: None,
                cert_file: None,
                key_file: None,
                strict_ssl: true,
            },
            ui: UiConfig {
                progress_bars: true,
                colors: true,
                unicode: true,
                verbose: false,
                quiet: false,
                log_level: "info".to_string(),
            },
            security: SecurityConfig {
                verify_checksums: true,
                allow_unsigned_packages: false,
                trusted_registries: vec!["https://registry.npmjs.org".to_string()],
                audit_level: "moderate".to_string(),
            },
        }
    }
}

impl NxConfig {
    // Load configuration from file or environment
    pub fn load() -> NxResult<Self> {
        let config_path = get_config_file_path();
        
        if config_path.exists() {
            let content = fs::read_to_string(&config_path)
                .map_err(|e| NxError::io_with_path(format!("Failed to read config file: {}", e), &config_path))?;
            
            let mut config: NxConfig = toml::from_str(&content)
                .map_err(|e| NxError::parse_with_context(
                    format!("Invalid config file format: {}", e),
                    config_path.display().to_string()
                ))?;
            
            // Override with environment variables
            config.apply_env_overrides();

            // Apply adaptive optimizations
            config.apply_adaptive_optimizations();

            Ok(config)
        } else {
            let mut config = Self::default();
            config.apply_env_overrides();
            config.apply_adaptive_optimizations();
            Ok(config)
        }
    }
    
    // Save configuration to file
    pub fn save(&self) -> NxResult<()> {
        let config_path = get_config_file_path();
        
        // Create config directory if it doesn't exist
        if let Some(parent) = config_path.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| NxError::io_with_path(format!("Failed to create config directory: {}", e), parent))?;
        }
        
        let content = toml::to_string_pretty(self)
            .map_err(|e| NxError::internal(format!("Failed to serialize config: {}", e)))?;
        
        fs::write(&config_path, content)
            .map_err(|e| NxError::io_with_path(format!("Failed to write config file: {}", e), &config_path))?;
        
        Ok(())
    }
    
    // Apply adaptive optimizations based on system capabilities
    pub fn apply_adaptive_optimizations(&mut self) {
        let cpu_cores = num_cpus::get();
        let available_memory = get_available_memory_mb();

        // Adaptive concurrency based on system resources
        if cpu_cores >= 8 && available_memory >= 8192 {
            // High-end system
            self.network.max_concurrent_downloads = 150;
            self.network.max_concurrent_per_host = 30;
            self.performance.max_parallel_extractions = cpu_cores * 2;
            self.performance.batch_size = 100;
        } else if cpu_cores >= 4 && available_memory >= 4096 {
            // Mid-range system
            self.network.max_concurrent_downloads = 100;
            self.network.max_concurrent_per_host = 20;
            self.performance.max_parallel_extractions = cpu_cores;
            self.performance.batch_size = 50;
        } else {
            // Low-end system
            self.network.max_concurrent_downloads = 50;
            self.network.max_concurrent_per_host = 10;
            self.performance.max_parallel_extractions = std::cmp::max(2, cpu_cores / 2);
            self.performance.batch_size = 25;
        }

        // Adaptive cache size based on available memory
        if available_memory >= 16384 {
            self.cache.max_size_mb = 2048; // 2GB
        } else if available_memory >= 8192 {
            self.cache.max_size_mb = 1024; // 1GB
        } else if available_memory >= 4096 {
            self.cache.max_size_mb = 512; // 512MB
        } else {
            self.cache.max_size_mb = 256; // 256MB
        }

        // Adaptive memory-mapped threshold
        if available_memory >= 8192 {
            self.performance.memory_mapped_threshold_mb = 10;
        } else if available_memory >= 4096 {
            self.performance.memory_mapped_threshold_mb = 5;
        } else {
            self.performance.memory_mapped_threshold_mb = 2;
        }
    }

    // Apply environment variable overrides
    fn apply_env_overrides(&mut self) {
        // Network overrides
        if let Ok(timeout) = std::env::var("NX_TIMEOUT") {
            if let Ok(timeout) = timeout.parse() {
                self.network.timeout_seconds = timeout;
            }
        }
        
        if let Ok(retries) = std::env::var("NX_MAX_RETRIES") {
            if let Ok(retries) = retries.parse() {
                self.network.max_retries = retries;
            }
        }
        
        if let Ok(concurrent) = std::env::var("NX_MAX_CONCURRENT") {
            if let Ok(concurrent) = concurrent.parse() {
                self.network.max_concurrent_downloads = concurrent;
            }
        }
        
        // Cache overrides
        if let Ok(cache_dir) = std::env::var("NX_CACHE_DIR") {
            self.cache.directory = PathBuf::from(cache_dir);
        }
        
        if let Ok(cache_size) = std::env::var("NX_CACHE_SIZE_MB") {
            if let Ok(cache_size) = cache_size.parse() {
                self.cache.max_size_mb = cache_size;
            }
        }
        
        // Registry overrides
        if let Ok(registry) = std::env::var("NX_REGISTRY") {
            self.registry.url = registry;
        }
        
        if let Ok(token) = std::env::var("NX_AUTH_TOKEN") {
            self.registry.auth_token = Some(token);
        }
        
        // UI overrides
        if let Ok(no_progress) = std::env::var("NX_NO_PROGRESS") {
            if no_progress == "1" || no_progress.to_lowercase() == "true" {
                self.ui.progress_bars = false;
            }
        }
        
        if let Ok(no_colors) = std::env::var("NX_NO_COLORS") {
            if no_colors == "1" || no_colors.to_lowercase() == "true" {
                self.ui.colors = false;
            }
        }
        
        if let Ok(verbose) = std::env::var("NX_VERBOSE") {
            if verbose == "1" || verbose.to_lowercase() == "true" {
                self.ui.verbose = true;
            }
        }
        
        if let Ok(quiet) = std::env::var("NX_QUIET") {
            if quiet == "1" || quiet.to_lowercase() == "true" {
                self.ui.quiet = true;
            }
        }
    }
    
    // Validate configuration
    pub fn validate(&self) -> NxResult<()> {
        // Validate network settings
        if self.network.timeout_seconds == 0 {
            return Err(NxError::validation_with_field("Timeout must be greater than 0", "network.timeout_seconds"));
        }
        
        if self.network.max_retries > 10 {
            return Err(NxError::validation_with_field("Max retries should not exceed 10", "network.max_retries"));
        }
        
        // Validate cache settings
        if self.cache.max_size_mb == 0 {
            return Err(NxError::validation_with_field("Cache size must be greater than 0", "cache.max_size_mb"));
        }
        
        // Validate registry URL
        if !self.registry.url.starts_with("http://") && !self.registry.url.starts_with("https://") {
            return Err(NxError::validation_with_field("Registry URL must start with http:// or https://", "registry.url"));
        }
        
        // Validate audit level
        let valid_audit_levels = ["none", "low", "moderate", "high", "critical"];
        if !valid_audit_levels.contains(&self.security.audit_level.as_str()) {
            return Err(NxError::validation_with_field(
                &format!("Audit level must be one of: {}", valid_audit_levels.join(", ")),
                "security.audit_level"
            ));
        }
        
        Ok(())
    }
}

// Global access functions
pub fn get_config() -> &'static NxConfig {
    &CONFIG
}

pub fn reload_config() -> NxResult<()> {
    // This would require a mutable static, which is complex
    // For now, config is loaded once at startup
    Ok(())
}

// Helper functions
fn get_config_file_path() -> PathBuf {
    if let Ok(config_file) = std::env::var("NX_CONFIG") {
        return PathBuf::from(config_file);
    }
    
    get_config_dir().join("config.toml")
}

fn get_config_dir() -> PathBuf {
    if let Some(config_dir) = std::env::var_os("XDG_CONFIG_HOME") {
        PathBuf::from(config_dir).join("nx")
    } else if let Some(home) = std::env::var_os("HOME") {
        PathBuf::from(home).join(".config").join("nx")
    } else if let Some(appdata) = std::env::var_os("APPDATA") {
        PathBuf::from(appdata).join("nx")
    } else {
        std::env::current_dir().unwrap_or_default().join(".nx")
    }
}

fn get_default_cache_dir() -> PathBuf {
    if let Some(cache_dir) = std::env::var_os("XDG_CACHE_HOME") {
        PathBuf::from(cache_dir).join("nx")
    } else if let Some(home) = std::env::var_os("HOME") {
        PathBuf::from(home).join(".cache").join("nx")
    } else if let Some(localappdata) = std::env::var_os("LOCALAPPDATA") {
        PathBuf::from(localappdata).join("nx").join("cache")
    } else {
        std::env::temp_dir().join("nx-cache")
    }
}

fn get_available_memory_mb() -> u64 {
    // Simplified memory detection - would use platform-specific APIs in production
    #[cfg(target_os = "linux")]
    {
        if let Ok(meminfo) = std::fs::read_to_string("/proc/meminfo") {
            for line in meminfo.lines() {
                if line.starts_with("MemAvailable:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = kb_str.parse::<u64>() {
                            return kb / 1024; // Convert to MB
                        }
                    }
                }
            }
        }
    }

    // Fallback: assume 8GB available
    8192
}
