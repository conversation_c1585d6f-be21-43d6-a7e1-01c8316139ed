{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 3592778941406178886, "path": 16215392639322834235, "deps": [[784494742817713399, "tower_service", false, 4788937879189487147], [1906322745568073236, "pin_project_lite", false, 8023542118904931378], [4121350475192885151, "iri_string", false, 2424441071081473483], [5695049318159433696, "tower", false, 4543005089213415137], [7712452662827335977, "tower_layer", false, 12325442436256052411], [7896293946984509699, "bitflags", false, 9087083678536015390], [9010263965687315507, "http", false, 1892032584777174270], [10629569228670356391, "futures_util", false, 17717833475852592514], [14084095096285906100, "http_body", false, 17240980880396472347], [16066129441945555748, "bytes", false, 6440951242105093202]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-http-2d350e115e87e99e\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}